package cn.yizhoucp.ump.api.vo.activity.galaxyAdventure;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.officialCombine.inner.GiftItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 银河熔炼兑换方案
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrizeStrategy {

    private Integer index;

    /**
     * 兑换策略
     */
    private List<PrizeItemStrategy> exchangeStrategy;

    /**
     * 兑换礼物
     */
    private PrizeItem exchangePrize;

    /**
     * 用户背包礼物是否充足
     */
    private Boolean enough;
}
