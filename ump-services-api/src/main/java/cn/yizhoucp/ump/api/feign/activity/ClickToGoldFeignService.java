package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.activity.clickToGold.IndexVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2023/2/14 14:59
 * @Version 1.0
 */
@FeignClient(value = "ump-services", contextId = "clickToGold")
public interface ClickToGoldFeignService {

    @GetMapping("/api/inner/activity/click-to-gold/get-index")
    Result<IndexVO> getIndex(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);


}
