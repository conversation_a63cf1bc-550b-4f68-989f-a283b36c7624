package cn.yizhoucp.ump.api.vo.crazyGift;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class CrazyGiftIndexVO {

    /**
     * 礼盒 小活动 礼盒和礼物前端直接放图 后端不需要返回
     */
//    private List<BoxVO> giftBoxList;

    /**
     * 送礼任务
     */
    private List<TaskItem> givingTaskList;

    /**
     * 收礼任务
     */
    private List<TaskItem> receiveTaskList;

    /**
     * 游乐园排行榜
     */
    private List<ParkBoard> parkBoardList;

    /**
     * 排行榜底部用户信息
     */
    private UserBoard userBoard;

    /**
     * 榜单奖励
     */
    private List<BoardPrize> boardPrizeList;

    private List<TaskRecord> givingTaskRecord;

    private List<TaskRecord> receiveTaskRecord;


    @Data
    public static class TaskRecord {
        //记录时间
        private String recordTime;

        private List<TaskRecordInfo> recordInfoList;

    }

    @Data
    public static class TaskRecordInfo {
        //礼物名称
        private String name;

        //送出礼物个数
        private Integer count;
    }


    @Data
    public static class BoxVO {
        private String name; //礼盒名称

        private String icon; //礼盒图标

        private Long price; //价格

        private List<CrazyGiftVO> giftList; //礼物
    }

    @Data
    public static class CrazyGiftVO {
        private String name; //礼物名称

        private String icon; //礼物图标

        private Long price; //金币
    }

    @Data
    public static class TaskItem {
        /**
         * 任务 code
         */
        private String taskCode;

        /**
         * 任务标题
         */
        private String taskTitle;

        /**
         * 任务需要送出的礼盒名称
         */
        private String taskBoxName;


        /**
         * 任务类型 0-每日任务 1-循环任务 2-终极任务
         */
        private Integer taskType;
        /**
         * 任务描述
         */
        private String taskDesc;

        /**
         * 当前完成次数
         */
        private Integer curFinishTimes;
        /**
         * 最大完成次数
         */
        private Integer maxFinishTimes;

        private Boolean receive;

        /**
         * 任务奖励名称
         */
        private String name;

        /**
         * 奖励 icon
         */
        private String icon;

        /**
         * 任务奖励价格
         */
        private Integer price;

        private Integer sortFlag;


    }

    @Data
    public static class ParkBoard {
        private String name;  //用户名称
        private Long value; //乐园值
        private String icon; //用户头像
        private Long rank;
    }

    @Data
    public static class UserBoard {
        private String name;
        private Long rank;
        private Long value; //乐园值
        private String icon; //用户头像
        private Long diffVal; //与上一名差值
    }

    @Data
    public static class BoardPrize {
        private Long rank;  //奖励的名次
        private String icon; //奖励图标
        private Long price; //奖励价值
    }

    @Data
    public static class GivingTaskRecord {
        private LocalDateTime recordTime;
        List<GiftRecordInfo> giftRecordInfoList;
    }

    /**
     * 收到/送出礼物记录
     */
    @Data
    public static class GiftRecordInfo {
        private String name;
        private Integer count;
    }

}
