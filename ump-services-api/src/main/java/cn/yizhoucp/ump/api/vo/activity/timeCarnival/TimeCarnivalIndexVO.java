package cn.yizhoucp.ump.api.vo.activity.timeCarnival;

import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.*;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TimeCarnivalIndexVO {
    private Integer coins;
    private Long faction;
    private List<Gift> drawGiftList;
    private ModernFamilyVO modernFamily;
    private NeonSymphonyVO neonSymphony;
    private NostalgiaKaraoke nostalgiaKaraoke;

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class NeonSymphonyVO {
        private Bands bands;
        private ScorePoint scorePoint;
        private List<Gift> giftList;
        private Long musicSheetNum;
        private ApprovalVO approvalVO;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ApprovalVO {
        private Boolean hasApproveList;
        private Boolean isLeader;
        private List<ApplyVO> applyList;
        private List<InviteVO> inviteList;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ApplyVO {
        private Long userId;
        private String userName;
        private String userAvatar;
    }
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class InviteVO {
        private String bandId;
        private String bandName;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ModernFamilyVO {
        private UserVO userInfo;
        private UserVO partnerInfo;
        private Long fashionValue;
        private List<Gift> giftList;
        private List<ExchangeGift> exchangeList;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ExchangeGift {
        private String giftKey;
        private String giftIcon;
        private Long giftNum;
        private Long exchangeNum;
        private Long exchangeCoin;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Gift {
        private String giftKey;
        private String giftName;
        private String giftIcon;
        private Long giftNum;
        private Long maxValue;
        private Long value;
        private Integer status;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class UserVO {
        private Long userId;
        private String avatar;
        private String userName;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Bands {
        private Long totalCount;
        private Long totalPage;
        private List<List<Band>> bandsList;
        private Band myBands;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Band {
        private String bandId;
        private String bandName;
        private Integer status;
        private Long score;
        private List<BandMember> bandMembers;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class BandMember {
        private Long userId;
        private String userName;
        private String userAvatar;
        private Long score;
        private Boolean isLeader;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ScorePoint {
        private Long score;
        private Boolean isLeader;
        private List<Point> points;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Point {
        private Integer pointIndex;
        private Integer pointStatus;
        private Integer pointValue;
        private Integer pointReward;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class NostalgiaKaraoke {
        private Long tapeCount;
        private List<Song> songs;
        private List<Task> tasks;
        private Integer status;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Song {
        private Integer songId;
        private String songName;
        private Boolean isLit;
        private Integer needTapeCount;

        public Song(Integer songId, String songName) {
            this.songId = songId;
            this.songName = songName;
        }
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Task {
        private Integer taskId;
        private Long status;
        private Integer totalProgress;
        private Long progress;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class TimeMachineRankVO {
        private Long retroTotalScore;
        private Long millenniumTotalScore;
        private Long modernTotalScore;
        private RankVO retroTimeMachineRank;
        private RankVO millenniumTimeMachine;
        private RankVO modernTimeMachine;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class BandRankVO {
        private List<BandRankItem> bandRankList;
        private BandRankItem myBandRankItem;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class BandRankItem {
        private Long rank;
        private String bandId;
        private String bandName;
        private Long score;
        private Long diffScore;
        private List<BandMember> bandMembers;
    }

}
