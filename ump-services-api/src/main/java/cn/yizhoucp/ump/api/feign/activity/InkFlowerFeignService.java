package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn;
import cn.yizhoucp.ump.api.vo.activity.Inkflower.InkFlowerIndexVO;
import cn.yizhoucp.ump.api.vo.activity.Inkflower.RollDiceReturn;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "inkFlower")
public interface InkFlowerFeignService {

    @GetMapping("/api/inner/activity/inkFlower/draw")
    Result<DrawReturn> draw(@RequestParam(value = "type",required = false) String type, @RequestParam(value = "poolCode",required = false) String poolCode, @RequestParam(value = "times",required = false) Integer times,@RequestParam(value = "extValue",required = false) String extValue);


    @GetMapping("/api/inner/inkFlower/claimWishReward")
    Result<List<InkFlowerIndexVO.Gift>> claimWishReward(@RequestParam("toUid") Long toUid, @RequestParam("prayer") String prayer);

    @GetMapping("/api/inner/inkFlower/rollDice")
     Result<RollDiceReturn> rollDice();

    @GetMapping("/api/inner/inkFlower/task/claimReward")
    Result<Boolean> claimReward(@RequestParam("taskKey") String taskKey,@RequestParam("isCollected") Boolean isCollected);


    /**
     * 绑定
     * @param toUid
     * @return
     */
    @GetMapping("/api/inner/inkFlower/bind")
    Result<Boolean> bind(@RequestParam(value = "toUid",required = false) Long toUid);

    /**
     * 获取好友
     * @param start
     * @param num
     * @return
     */
    @GetMapping("/api/inner/inkFlower/myFriends")
    Result<List<InkFlowerIndexVO.User>> myFriends(@RequestParam(value = "start",required = false) Long start, @RequestParam(value = "num",required = false) Integer num);

    /**
     * 购买记录
     * @return
     */
    @GetMapping("/api/inner/inkFlower/purchaseRecord")
     Result<Object> purchaseRecord();

    /**
     * 梅花商店购买
     * @param itemId
     * @param quantity
     * @return
     */
    @GetMapping("/api/inner/inkFlower/buyItem")
     Result<cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn> buyItem(@RequestParam("itemId") Integer itemId, @RequestParam("quantity") Integer quantity);


    @GetMapping("/api/inner/activity/inkFlower/get-rank")
     Result<RankVO> getTotalRank();

    @GetMapping("/api/inner/activity/inkFlower/send-prize")
    Result<Boolean> sendPrize();

    @GetMapping("/api/inner/inkFlower/drawRecord")
    Result<Object> drawRecord();


}
