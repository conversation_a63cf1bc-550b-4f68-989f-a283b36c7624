package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ump.api.vo.callback.LuckyBagCallBackVO;
import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 福袋唤回活动
 *
 * @author: lianghu
 */
@FeignClient(value = "ump-services", contextId = "luckyBagCallbackActivity")
public interface LuckyBagCallBackFeignService {

    @GetMapping("/api/inner/activity/lucky-bag-call-back/get-index-info")
    Result<LuckyBagCallBackVO> getIndexInfo(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId);

    @GetMapping("/api/inner/activity/lucky-bag-call-back/active-condition")
    Result<Boolean> activeCondition(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

}
