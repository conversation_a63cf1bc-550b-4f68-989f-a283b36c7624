package cn.yizhoucp.ump.api.vo.goddessTrain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/8/26 11:08
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class GoddessTrainUserActivityVO {

    /** 应用 ID */
    private Long appId;
    /** 用户ID */
    private Long uid;
    private String unionId;
    /** 活动编号 */
    private String activityCode;
    /** 活动名称 */
    private String activityName;
    /** 开始时间 */
    private LocalDateTime startTime;
    /** 结束时间 */
    private LocalDateTime endTime;

}
