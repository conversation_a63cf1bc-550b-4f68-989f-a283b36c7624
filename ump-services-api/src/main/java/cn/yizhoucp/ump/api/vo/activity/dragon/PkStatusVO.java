package cn.yizhoucp.ump.api.vo.activity.dragon;

import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/12 14:55
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class PkStatusVO implements Serializable {

    /** -1 未家任何家族 ；0 家族未上榜；1 上榜 */
    private Integer status;
    private Integer threshold;
    /** pk 列表 */
    private List<RankItem> rankItemList;
    /** 榜单详情 */
    private RankVO rankVO;

}
