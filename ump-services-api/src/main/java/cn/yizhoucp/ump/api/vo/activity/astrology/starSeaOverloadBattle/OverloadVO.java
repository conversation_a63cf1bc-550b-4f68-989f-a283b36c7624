package cn.yizhoucp.ump.api.vo.activity.astrology.starSeaOverloadBattle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OverloadVO implements Serializable {

    private static final long serialVersionUID = -3507765916101951982L;

    private String icon;
    private String name;
    private Long value;
    private Long countdownTimestamp;

    private Long uid;
    private Integer addStarSeaValueTimes;

}
