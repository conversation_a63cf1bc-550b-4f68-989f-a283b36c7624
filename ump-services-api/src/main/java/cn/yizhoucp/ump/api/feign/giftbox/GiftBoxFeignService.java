package cn.yizhoucp.ump.api.feign.giftbox;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "gift-box")
public interface GiftBoxFeignService {

    @RequestMapping("/api/inner/activity/gift-box/draw")
    Result<DrawReturn> draw(@RequestParam("activityCode") String activityCode, @RequestParam("type") String type, @RequestParam("poolCode") String poolCode, @RequestParam("times") Integer times, @RequestParam("extValue") String extValue);

}
