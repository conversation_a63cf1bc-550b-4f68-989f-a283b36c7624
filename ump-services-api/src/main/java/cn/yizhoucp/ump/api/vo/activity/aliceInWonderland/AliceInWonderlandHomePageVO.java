package cn.yizhoucp.ump.api.vo.activity.aliceInWonderland;

import lombok.Data;

import java.util.List;

@Data
public class AliceInWonderlandHomePageVO {

    private Integer camp;
    /**
     * 阵营榜单
     */
    private List<CampValueVO> campValue;

    /**
     * 用户排名
     */
    private UserRankVO userRank;

    /**
     * 前五名礼物
     */
    private List<RankGiftVO> topFiveGifts;

    /**
     * 中奖信息
     */
    private Object broadcast;

    /**
     * 奖池
     */
    private List<AliceDrawPoolVO> drawPools;

    /**
     * 梦游仙境活动页
     */
    private DreamLandVO dreamLand;

    /**
     * 绿野仙踪活动页
     */
    private GreenPathVO greenPath;

    /**
     * 深林奇遇活动页
     */
    private ForestWonderlandVO forestWonderland;


}
