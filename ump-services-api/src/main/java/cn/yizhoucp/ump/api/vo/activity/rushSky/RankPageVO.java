package cn.yizhoucp.ump.api.vo.activity.rushSky;

import cn.yizhoucp.ump.api.vo.activity.rushSky.inner.FamilyUserRankItem;
import cn.yizhoucp.ump.api.vo.activity.rushSky.inner.FamilyRankItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 榜单页信息
 *
 * @author: lianghu
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RankPageVO {

    /** 家族荣誉榜 */
    private List<FamilyRankItem> familyRankInfo;
    /** 家族贡献榜 - 昨日最佳 */
    private FamilyUserRankItem yesterdayNo1;
    /** 家族贡献榜 */
    private List<FamilyUserRankItem> familyUserRankInfo;

}
