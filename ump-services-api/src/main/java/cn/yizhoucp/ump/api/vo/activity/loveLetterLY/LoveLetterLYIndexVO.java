package cn.yizhoucp.ump.api.vo.activity.loveLetterLY;

import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LoveLetterLYIndexVO {

    /* 情书数量 */
    private Long letterCount;

    private UserItem user;
    private UserItem partner;

    /* 情书商店商品 */
    private List<Product> productList;

    /* 暖心礼物状态 */
    private Integer warmGiftStatus;

    /* 巧克力 */
    private List<Chocolate> chocolateList;

    /* 材料 */
    private List<Material> materialsList;

    private RankVO rank;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Chocolate {
        private String chocolateKey;
        private Integer chocolateStatus;
        /* 配方keys */
        private List<String> associatedMaterialList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Material {
        private String materialsKey;
        private String materialsName;
        private Long materialsNum;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserItem {
        private String avatar;
        private String userName;
        private String userId;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product {
        private String key;
        private String name;
        private String icon;
        private Long price;

        /* 情书数量 */
        private Long quantityRequired;
    }

}
