package cn.yizhoucp.ump.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 活动清单
 *
 * @return
 */
@Getter
@AllArgsConstructor
public enum ActivityCheckListEnum {

    /**
     * 活动枚举 对应数据库 activity_check_list 尽量常驻活动进行添加
     */
    DEFAULT("default", "兜底", "兜底"),
    DOUBLE_EGG("double_egg", "simple.activity.double.egg.tips", "双旦活动"),
    RICHMAN("richman", "simple.activity.richman.tips", "大富翁活动"),
    LUCKY_BAG_HIGH_PROBABILITY("astrology-high-probability", "simple.activity.high.probability.tips", "占星术高爆活动"),
    LUCKY_BAG_WITH_RULE("astrology", "simple.activity.high.probability.tips", "占星术"),
    LUCKY_BAG_WITH_ALCHEMY("lucky-bag-with-alchemy", "simple.activity.high.probability.tips", "福袋炼金炉"),
    LUCKY_BAG_CALL_BACK("lucky-bag-call-back", "simple.activity.lucky.bag.back.tips", "福袋召回活动"),
    BIG_R_CALL_BACK("big-r-call-back", "simple.activity.big.r.back.tips", "大R召回活动"),

    LUCKY_BAG_DIAMOND("lucky_bag_diamond", "simple.lucky.bag.name", ""),
    MAGPIE("magpie", "七夕集喜鹊", "七夕集喜鹊"),

    LUCKY_BAG_TESK_REWARD("lucky_bag_task_reward", "simple.lucky.bag.name", ""),
    GODDESS_TRAIN("goddess-train", "simple.goddess.train.name", "女生作业"),
    LUCKY_BAG_CALL_BACK_DAILY("lucky-bag-call-back-daily-new", "", "福袋唤回日常活动"),

    AUTUMN_FESTIVAL("autumn-festival", "simple.autumn.festival.name", "玉兔带你来告白"),
    iOS_RECHARGE_GUIDE("iOS-recharge-guide", "", "iOS 充值引导"),
    LOVE_POSTCARD("love-postcard", "", "国庆明信片活动"),
    RUSH_SKY("rush-sky", "", "冲上云霄"),
    GODDESS_TRAIN_APPOINTMENT("goddess-train-appointment", "simple.goddess.train.appointment.name", "女生预约报名"),
    THANKS_GIVING("thanks-giving", "simple.activity.thanks.giving.tips", "感恩节活动"),
    PLANT_TREES("plant-trees", "simple.activity.plant.trees.tips", "合种爱情树"),
    NEW_YEAR_2022("new-year-2022", "", "新年上上签"),
    LUCKY_BAG_FIGHT("lucky-bag-fight", "", "福袋双旦"),
    LUCKY_CHARM("lucky-charm", "", "春节锦鲤"),
    SWEET_CP("sweet-cp", "", "甜蜜情侣1V1"),
    CLICK_TO_GOLD("click-to-gold", "", "通天塔之点石成金"),
    LUCKY_BAG_WALL("lucky-bag-wall", "", "福袋墙活动"),
    CARD_COLLECT("card-collect", "", "幸运集卡大挑战"),
    LOVE_PROMISE("love-promise", "", "官宣定情"),
    ASTROLOGY_CARD_COLLECT("astrology-card-collect", "", "塔罗心愿集卡"),
    GODDESS_FESTIVAL_2023("goddess-festival-2023", "", "2023 女神节"),
    WU_LIN_HEGEMONY("wu-lin-hegemony", "", "武林争霸活动"),
    GLORY_CEREMONY("glory-ceremony", "", "春季荣耀盛典"),
    OFFICIAL_COMBINE("official-combine", "", "官宣房"),
    LUCKY_BAG_HUNT("lucky-bag-hunt", "", "福袋寻宝活动"),
    UNPRECEDENTED_LOVE("unprecedented-love", "", "旷世恋人活动"),
    SAKA_WEEK_STAR("saka-week-star", "", "saka礼物周星活动"),
    LUCKY_BAG_FALL("lucky-bag-fall", "", "福气降临活动"),
    ACTIVITY_520("activity-520", "", "520活动"),
    MAGPIE2023("magpie2023", "simple.activity.magpie2023.tips", "2023七夕活动"),
    TWINS("twins", "", "守护双子座活动"),
    DRAGON("dragon", "端午节活动", "端午节活动"),
    ASTROLOGER_CHALLENGE("astrologer-challenge", "占星师挑战活动", "占星师挑战活动"),
    ASTROLOGY_ADVENTURE("astrology-adventure", "占星奇旅", "占星奇旅"),
    MID_AUTUMN2023("mid-autumn2023", "2023中秋国庆活动", "2023中秋国庆活动"),
    MISTY_STAR_CONTINENT("misty_star_continent", "迷雾星洲", "迷雾星洲"),
    HALLOWEEN2023("halloween2023", "万圣狂欢夜", "万圣狂欢夜"),
    STARRY_SEA("starry-sea", "幻月星海", "幻月星海"),
    THANKSGIVING_BATTLE("thanksgiving_battle", "感恩节大作战", "感恩节大作战"),
    GUARD_CONSTELLATION("guard_constellation", "守护星座", "守护星座"),
    CURETTAGE("curettage", "占星刮刮乐", "占星刮刮乐"),
    ASTROLOGY_CARNIVAL_MONTH("astrology_carnival_month", "占星狂欢月", "占星狂欢月"),
    DOUBLE_EGG2023("double_egg2023", "双旦送暖迎新岁", "双旦送暖迎新岁"),
    STAR_SEA_TOUR("star_sea_tour", "星海巡游记", "星海巡游记"),
    STAR_WISH_CARD_COLLECTION_GAME("star_wish_card_collection_game", "星愿集卡乐", "星愿集卡乐"),
    SPRING_FESTIVAL_2024("spring_festival_2024", "2024新春活动", "2024新春活动"),
    STAR_SPRING("star_spring", "祥龙迎星春", "祥龙迎星春"),
    TOGETHER_IN_2024("together_in_2024", "2024在一起", "2024在一起"),
    MAY_CONFESSION("may_confession", "五月告白季", "五月告白季"),
    GIRL_S_DAY("girlsDay", "女生节", "女生节"),
    STAR_SEA_OVERLOAD_BATTLE("star_sea_overload_battle", "星海霸主战", "星海霸主战"),
    SWEET_ROOM_BOX("sweet_room_box", "甜蜜小屋抽奖", "甜蜜小屋抽奖"),
    LEGENDARY_STAR_PARK_COIN("legendary_star_park","传说中的星乐园","传说中的星乐园"),
    LEGENDARY_NEVERLAND("legendary_neverland", "传说中的梦幻岛", "传说中的梦幻岛"),
    ;

    private static final Map<String, ActivityCheckListEnum> codeMap = new ConcurrentHashMap<>();

    static {
        for (ActivityCheckListEnum item : values()) {
            codeMap.put(item.getCode(), item);
        }
    }

    private String code;
    private String descWithInternational;
    private String desc;

    public static ActivityCheckListEnum getInstanceByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return MAGPIE2023;
        }
        ActivityCheckListEnum activityCheckListEnum = codeMap.get(code);
        if (Objects.isNull(activityCheckListEnum)) {
            return MAGPIE2023;
        }
        return activityCheckListEnum;
    }

}
