package cn.yizhoucp.ump.api.vo.officialCombine.param;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.officialCombine.inner.GiftItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 提交购物车入参
 *
 * @author: lianghu
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ConfirmCartParam extends BaseParam {

    /** 是否确认提交 */
    private Boolean confirm;
    /** 对方 uid */
    private Long toUid;
    /** 定情信物 ID */
    private Long loveTokenId;
    /** 限时返场礼物列表 */
    private List<GiftItem> limitGiftList;
    /** 礼物面板礼物列表 */
    private List<GiftItem> panelGiftList;

}
