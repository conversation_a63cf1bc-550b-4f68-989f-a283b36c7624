package cn.yizhoucp.dto.activitySkinDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ActivitySkinResourceRequestDTO {

    @NotNull(message = "资源key不能为空")
    private String resourceKey;

    @NotNull(message = "配置不能为空")
    private Map<String, Object> resourceConfig;
}
