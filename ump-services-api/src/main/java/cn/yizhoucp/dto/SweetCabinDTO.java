package cn.yizhoucp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 官宣甜蜜小屋信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SweetCabinDTO {

    private Long id;
    private Long appId;
    /** 男方用户id */
    private Long maleUid;
    /** 女方用户id */
    private Long femaleUid;
    /** 男方用户最近活跃时间 */
    private Long maleActiveTime;
    /** 女方用户最近活跃时间 */
    private Long femaleActiveTime;
    /** 男方用户最近进入小屋时间 */
    private Long maleEnterCabinTime;
    /** 女方用户最近进入小屋时间 */
    private Long femaleEnterCabinTime;
    /** 男方用户最近操作小屋时间 */
    private Long maleOperateTime;
    /** 女方用户最近操作小屋时间 */
    private Long femaleOperateTime;
    /** 双方id */
    private String splicUid;
    /** 小屋等级 */
    private Integer level;
    /** 当前恋屋币余额 */
    private Long lhCoin;
    /** 历史获取恋屋币总额 */
    private Long lhCoinTotal;
    /** 甜蜜值 */
    private Long sweetValue;
    /** 下一等级甜蜜值 */
    private Long nextSweetValue;
    /** 靓屋值 */
    private Long beautifulValue;
    /** 下一等级靓屋值 */
    private Long nextBeautifulValue;
    /** 浪漫值 */
    private Long romanticValue;
    /** 小屋是否公开(true-公开/false-隐藏) */
    private Boolean cabinStatus;
    /** 状态 */
    private String status;

}
