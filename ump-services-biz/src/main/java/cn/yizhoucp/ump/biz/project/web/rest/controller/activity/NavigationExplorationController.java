package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.activity.navigationExploration.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.exploration.NavigationExplorationManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.exploration.NavigationExplorationRankManager;
import io.swagger.annotations.Authorization;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class NavigationExplorationController {
    @Resource
    private NavigationExplorationManager navigationExplorationManager;

    @Resource
    private NavigationExplorationRankManager navigationExplorationRankManager;

    @GetMapping("/api/inner/ump/navigation_exploration/index")
    public Result<IndexVO> index() {
        SecurityUser currentUser = SecurityUtils.getCurrentUser();
        return RestBusinessTemplate.executeWithoutTransaction(() -> navigationExplorationManager.index(currentUser.getUserId()));
    }

    @GetMapping("/api/inner/ump/navigation_exploration/open-box")
    public Result<PrizeItem> openBox(@RequestParam("boxIndex") Integer boxIndex) {
        SecurityUser currentUser = SecurityUtils.getCurrentUser();
        return RestBusinessTemplate.executeWithoutTransaction(() -> navigationExplorationManager.openBox(currentUser.getUserId(), boxIndex));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/ump/job/navigation_exploration/give-rank-gift")
    public Result<Boolean> giveRankGift() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> navigationExplorationRankManager.postGiftYesterday());
    }
}
