package cn.yizhoucp.ump.biz.project.biz.enums.activity.valentine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName: ValentineWomanDrawPrizePoolEnum
 * @Description: 情人节女生礼物抽奖池
 * @author: pepper
 * @date: 2022/1/25 15:40
 */
@AllArgsConstructor
@Getter
public enum ValentineWomanDrawPrizePoolEnum {

    BMG_ITEM_GIFT(ValentinePrizeEnum.MGWY_GIFT, 1, 20, null, 0, 150),
    MKL_ITEM_GIFT(ValentinePrizeEnum.MKL_GIFT, 1, 20, null, 151, 198),
    LMXH_ITEM_GIFT(ValentinePrizeEnum.LMXH_GIFT, 1, 1, null, 199, 258),
    BYSF_ITEM_GIFT(ValentinePrizeEnum.FYXJ_GIFT, 1, 1, null, 259, 318),
    ;
    /**
     * 奖池总大小
     */
    public static final Integer totalPoolNum = 319;

    /**
     * 礼物类型
     */
    private ValentinePrizeEnum prize;
    /**
     * 奖品数量
     */
    private Integer num;
    /**
     * 有效天数
     */
    private Integer effectiveDays;
    /**
     * 扩展信息
     */
    private String extData;
    /**
     * gifyNum min
     */
    private Integer min;
    /**
     * gifyNum max
     */
    private Integer max;

    public static ValentineWomanDrawPrizePoolEnum getInstance(Integer giftNum) {
        for (ValentineWomanDrawPrizePoolEnum item : values()) {
            if (item.getMin() <= giftNum && giftNum <= item.getMax()) {
                return item;
            }
        }
        return null;
    }
}
