package cn.yizhoucp.ump.biz.project.biz.manager.officialCombine;


import cn.yizhoucp.family.api.client.FamilyInfoFeignService;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ms.core.vo.productServices.PackageProductVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.order.api.client.NormalOrderFeignService;
import cn.yizhoucp.order.api.vo.OrderDetailVO;
import cn.yizhoucp.order.api.vo.OrderVO;
import cn.yizhoucp.ump.api.vo.officialCombine.admin.OcAdminVO;
import cn.yizhoucp.ump.api.vo.officialCombine.enums.CombineStatusEnum;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.util.PageUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.OfficialCombineUserJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.OfficialCombineUserDO;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class OfficialCombineAdminManager {

    @Resource
    private OfficialCombineUserJpaDAO officialCombineUserJpaDAO;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private FamilyInfoFeignService familyInfoFeignService;
    @Resource
    private NormalOrderFeignService normalOrderFeignService;

    public AdminPageVO<OcAdminVO> listByPage(OcAdminVO param, Integer page, Integer size) {
        Page<OfficialCombineUserDO> pageResult = officialCombineUserJpaDAO.findAll(getPageDetailInfoSql(param), PageUtil.defaultPage(page - 1, size, "createTime"));
        if (CollectionUtils.isEmpty(pageResult.getContent())) {
            return AdminPageVO.getDefault(page, size, pageResult);
        }
        // 数据结构转换
        List<OcAdminVO> data = pageResult.getContent().stream().map(this::convert2AdminVO).collect(Collectors.toList());
        // 剔除空元素
        data = data.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return AdminPageVO.getDefault(page, size, pageResult, data);
    }

    private Specification<OfficialCombineUserDO> getPageDetailInfoSql(OcAdminVO param) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<javax.persistence.criteria.Predicate> predicates = new ArrayList<>();
            if (Objects.nonNull(param.getMaleId())) {
                predicates.add(criteriaBuilder.equal(root.get("maleUid"), param.getMaleId()));
            }
            if (Objects.nonNull(param.getFemaleId())) {
                predicates.add(criteriaBuilder.equal(root.get("femaleUid"), param.getFemaleId()));
            }
            if (Objects.nonNull(param.getOcStartTime()) && Objects.nonNull(param.getOcEndTime())) {
                LocalDateTime ocStartTime = Instant.ofEpochMilli(Long.parseLong(param.getOcStartTime())).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
                LocalDateTime ocEndTime = Instant.ofEpochMilli(Long.parseLong(param.getOcEndTime())).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
                predicates.add(criteriaBuilder.between(root.get("createTime"), ocStartTime, ocEndTime));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public OcAdminVO convert2AdminVO(OfficialCombineUserDO param) {
        // 查询用户信息
        UserBaseVO maleUser = userRemoteService.getBasicAll(param.getAppId(), param.getMaleUid(), Boolean.FALSE);
        UserBaseVO femaleUser = userRemoteService.getBasicAll(param.getAppId(), param.getFemaleUid(), Boolean.FALSE);
        if (Objects.isNull(maleUser) || Objects.isNull(femaleUser)) {
            return null;
        }

        // 处理官宣信息
        String startTime = "";
        String endTime = "";
        String ocStartTime = "";
        if (Objects.nonNull(param.getStartTime())) {
            startTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(param.getStartTime());
        }
        if (Objects.nonNull(param.getEndTime())) {
            endTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(param.getEndTime());
        }
        ocStartTime =  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(param.getCreateTime());

        // 查询订单信息
        String orderDetailDesc = "";
        String orderPayPrice = "";
        if (Objects.nonNull(param.getOrderId())) {
            OrderVO orderVO = normalOrderFeignService.getOrder(param.getOrderId()).successData();
            if (Objects.nonNull(orderVO)) {
                orderPayPrice = Objects.nonNull(orderVO.getOrderPayPrice()) ? orderVO.getOrderPayPrice().toString() : "";
                orderDetailDesc = getOrderDetailDesc(orderVO.getOrderDetails());
            }
        }

        return OcAdminVO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .startTime(startTime)
                .endTime(endTime)
                .ocStartTime(ocStartTime)
                .maleId(maleUser.getId())
                .maleName(maleUser.getName())
                .femaleId(femaleUser.getId())
                .femaleName(femaleUser.getName())
                .maleFamily(Objects.isNull(param.getMaleFamilyId()) || Objects.equals(-1L, param.getMaleFamilyId()) ? "" : param.getMaleFamilyId().toString())
                .femaleFamily(Objects.isNull(param.getFemaleFamilyId()) || Objects.equals(-1L, param.getFemaleFamilyId()) ? "" : param.getFemaleFamilyId().toString())
                .orderDetailDesc(orderDetailDesc)
                .status(CombineStatusEnum.get(param.getStatus()).getDesc())
                .orderPayPrice(orderPayPrice).build();
    }

    private String getOrderDetailDesc(List<OrderDetailVO> orderDetails) {
        StringBuilder desc = new StringBuilder();
        if (CollectionUtils.isEmpty(orderDetails)) {
            return desc.toString();
        }
        for (OrderDetailVO item : orderDetails) {
            PackageProductVO product = JSONObject.parseObject(item.getProductSnapshot(), PackageProductVO.class);
            if (Objects.isNull(product)) {
                continue;
            }
            desc.append(product.getName()).append("*").append(item.getNum()).append("、");
        }
        return StringUtils.removeEnd(desc.toString(), "、");
    }

}
