package cn.yizhoucp.ump.biz.project.biz.manager.activity.kingAdvanced;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.KingAdvancedConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.KingAdvancedConstant.ACTIVITY_CODE;

@Service
@Slf4j
public class KingAdvancedRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private ScenePrizeService scenePrizeService;

    @Resource
    private KingAdvancedDrawManager kingAdvancedDrawManager;

    @Resource
    private KingAdvancedTrackManager kingAdvancedTrackManager;

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(10L);
    }

    /**
     * 发放王者榜排名奖励
     */
    public Boolean sendKingRankJob() {
        // 王者榜 -- 每日结算
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "rank_king");
        log.info("scenePrizeDOList King: {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.warn("scenePrizeJpaDAO getListBySceneCode is null sceneCode : rank_king");
            return Boolean.FALSE;
        }
        String kingLeaderboardRedisKey = "king:kingLeaderboard:date:%s:kingThose";
        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(String.format(kingLeaderboardRedisKey, dayBeforeToStr()))
                .type(RankContext.RankType.user)
                .build());
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            log.warn("king rankList is null");
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());

            String key = "king:drawCountDate:date:%s:user:%s";
            String userDateDrawCount = String.format(key, dayBeforeToStr(),rankItem.getId());
            if (!redisManager.hasKey(userDateDrawCount)) {
                log.warn("user king is null:{}", userDateDrawCount);
                return Boolean.FALSE;
            }
            Long aLong = redisManager.getLong(userDateDrawCount);
            log.info("user king score:{}", aLong);
            if (CollectionUtils.isEmpty(scenePrizeDOs) || aLong < 80L) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }
            log.info("user king scene PrizeDO List:{}", scenePrizeDOs);
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );
            // 增加埋点
            ScenePrizeDO scenePrizeDO = scenePrizeDOs.get(0);
            kingAdvancedTrackManager.allActivityReceiveAward(rankItem.getId(), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), 1);
            // 消息小助手发送消息
            String msgByNPC = "恭喜您在“王者进阶舱”活动中，上榜【王者榜】第%s名，获得价值“%s”的礼物”%s“，有效期15天，请注意查收";
            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId()
                    , String.format(msgByNPC, rank, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc()));
            Result<UserVO> result = feignUserService.getBasic(rankItem.getId(), ServicesAppIdEnum.lanling.getAppId());
            if (null == result || !StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), result.getCode())) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
            UserVO userVO = result.getData();
            String msgByFamilyNPC = String.format("恭喜家族成员%s在“王者进阶舱”活动中，上榜【王者榜】第%s名，获得价值“%s”的礼物”%S“，速去围观"
                    , userVO.getName(), rank, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc());
            kingAdvancedDrawManager.sendFamilyMsg(userVO.getId(), msgByFamilyNPC);

            // 榜单弹窗
            String kingWindowKey = KingAdvancedConstant.createKingWindowKey(rankItem.getId());
            log.info("kingWindowKey: {}", kingWindowKey);
            redisManager.set(kingWindowKey, rank, DateUtil.ONE_DAY_SECOND - 1L);
        }

        return Boolean.TRUE;
    }

    public String dayBeforeToStr() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return yesterday.format(formatter);
    }


    /**
     * 发放终极榜奖励
     *
     * @return
     */
    public Boolean sendUltimateRankJob() {
        // 终极榜，最后一天结算
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "rank_ultimate");
        log.info("scenePrizeDOList Ultimate: {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.warn("scenePrizeJpaDAO getListBySceneCode is null sceneCode : rank_ultimate");
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(KingAdvancedConstant.KING_ULTIMATE_KEY)
                .type(RankContext.RankType.user)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            log.info("rankList is null");
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());

            String userDateDrawCount = KingAdvancedConstant.createUserDrawCount(rankItem.getId());
            if (!redisManager.hasKey(userDateDrawCount)) {
                log.warn("user ultimate is null:{}", userDateDrawCount);
                return Boolean.FALSE;
            }
            Long aLong = redisManager.getLong(userDateDrawCount);
            log.info("user ultimate score:{}", aLong);
            if (CollectionUtils.isEmpty(scenePrizeDOs) || aLong < 720L) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }
            log.info("scene one PrizeDO List:{}", scenePrizeDOs);
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())

            );
            // 增加埋点
            ScenePrizeDO scenePrizeDO = scenePrizeDOs.get(0);
            kingAdvancedTrackManager.allActivityReceiveAward(rankItem.getId(), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), 1);
            // 消息小助手发送消息
            String msgByNPC = "恭喜您在“王者进阶舱”活动中，上榜【终极榜】第%s名，获得价值“%s”的礼物”%s“，有效期15天，请注意查收";
            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId()
                    , String.format(msgByNPC, rank, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc()));
            Result<UserVO> result = feignUserService.getBasic(rankItem.getId(), ServicesAppIdEnum.lanling.getAppId());
            if (null == result || !StringUtils.equalsIgnoreCase(ErrorCode.SUCCESS.getCode(), result.getCode())) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
            UserVO userVO = result.getData();
            String msgByFamilyNPC = String.format("恭喜家族成员%s在“王者进阶舱”活动中，上榜【终极榜】第%s名，获得价值“%s”的礼物”%S“，速去围观"
                    , userVO.getName(), rank, scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeDesc());
            kingAdvancedDrawManager.sendFamilyMsg(userVO.getId(), msgByFamilyNPC);

            // 需要榜单弹窗
            String ultimateWindowKey = String.format(KingAdvancedConstant.ULTIMATE_WINDOW_KEY, rankItem.getId());
            log.info("ultimateWindowKey: {}", ultimateWindowKey);
            redisManager.set(ultimateWindowKey, rank, DateUtil.ONE_MONTH_SECOND);
        }
        return Boolean.TRUE;
    }
}
