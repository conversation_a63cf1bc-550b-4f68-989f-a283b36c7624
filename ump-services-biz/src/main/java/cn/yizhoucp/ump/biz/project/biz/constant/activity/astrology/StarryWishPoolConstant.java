package cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class StarryWishPoolConstant {

    public static final String ACTIVITY_CODE = "starry_wish_pool";

    /**
     * uid
     * yyyyMMdd
     */
    public static final String ASTROLOGY_TIMES = "ump:starry_wish_pool:astrology_times:%s:%s";

    public static final String REPLACE_GIFT = "ump:starry_wish_pool:replace_gift";

    /**
     * yyyyMMdd
     */
    public static final String RANK_DAY = "ump:starry_wish_pool:rank_day:%s";

    public static final String RANK_CUSTOM_1 = "ump:starry_wish_pool:rank_custom_1";
    public static final String RANK_CUSTOM_2 = "ump:starry_wish_pool:rank_custom_2";

    /**
     * uid
     * yyyyMMdd
     * Box.name()
     */
    public static final String BOX_OPEN = "ump:starry_wish_pool:box_open:%s:%s:%s";
    @AllArgsConstructor
    @Getter
    public enum Box {
        BOX_1(50),
        BOX_2(288),
        BOX_3(1288),
        BOX_4(2588),
        BOX_5(5888),
        ;

        private final int astrologyTimes;
    }

    /**
     * uid
     * Task.name()
     */
    public static final String TASK_CUR_FINISH_TIMES = "ump:starry_wish_pool:task_cur_finish_times:%s:%s";
    public static final String TASK_TAKE_PRIZE = "ump:starry_wish_pool:task_take_prize:%s:%s";
    @AllArgsConstructor
    @Getter
    public enum Task {
        TASK_1(14),
        TASK_2(12),
        TASK_3(10),
        ;

        private final int maxFinishTimes;
    }

    /**
     * CountryTask.name()
     */
    public static final String COUNTRY_TASK_CUR_FINISH_TIMES = "ump:starry_wish_pool:country_task_cur_finish_times:%s";
    @AllArgsConstructor
    @Getter
    public enum CountryTask {
        COUNTRY_TASK_1("NSJZ_GIFT_ASTROLOGY", "astrology_times", 10000, null),
        COUNTRY_TASK_2("LAPJ_GIFT_ASTROLOGY", "astrology_times", 15000, null),
        COUNTRY_TASK_3("YYST_GIFT_ASTROLOGY", "astrology_times", 20000, null),
        COUNTRY_TASK_4("JJZS_GIFT_ASTROLOGY", "astrology_times", 35000, null),
        COUNTRY_TASK_5("SFAN_GIFT_ASTROLOGY", "gift_count", 10, 5200L),
        COUNTRY_TASK_6("HHBF_GIFT_ASTROLOGY", "gift_count", 5, 9999L),
        COUNTRY_TASK_7("HHPD_GIFT_ASTROLOGY", "gift_count", 3, 19999L),
        COUNTRY_TASK_8("YQHP_GIFT_ASTROLOGY", "astrology_times", 10000, null),
        COUNTRY_TASK_9("XC_GIFT_ASTROLOGY", "astrology_times", 15000, null),
        COUNTRY_TASK_10("FLSM_GIFT_ASTROLOGY", "astrology_times", 20000, null),
        COUNTRY_TASK_11("YRRJ_GIFT_ASTROLOGY", "astrology_times", 35000, null),
        COUNTRY_TASK_12("KFZW_GIFT_ASTROLOGY", "gift_count", 10, 5200L),
        COUNTRY_TASK_13("YNYW_GIFT_ASTROLOGY", "gift_count", 5, 9999L),
        COUNTRY_TASK_14("SZYZ_GIFT_ASTROLOGY", "gift_count", 3, 19999L),
        COUNTRY_TASK_15("SZYZ_GIFT_ASTROLOGY", "gift_count", 3, 19999L),
        ;

        private final String prizeKey;
        private final String type;
        private final int targetValue;
        private final Long giftCoin;
    }

}
