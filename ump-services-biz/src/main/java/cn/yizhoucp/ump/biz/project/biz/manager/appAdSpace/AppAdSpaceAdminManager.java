package cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace;

import cn.yizhoucp.ms.core.base.Constant;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.EnvType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.model.UserPermission;
import cn.yizhoucp.ms.core.base.util.MD5;
import cn.yizhoucp.ms.core.vo.AdminRemoteConfigVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.appservices.ActivityBannerProperty;
import cn.yizhoucp.ms.core.vo.appservices.SplashProperty;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.AdSpaceTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.AdSpaceJumpTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.AdSpaceShowTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.AdSpaceStatusEnum;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignChatieService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.biz.util.MD5Util;
import cn.yizhoucp.ump.biz.project.biz.util.PageUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.AppAdSpaceJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.AppAdSpaceDO;
import cn.yizhoucp.ump.biz.project.dto.GiftBannerModel;
import cn.yizhoucp.ump.biz.project.web.vo.AppAdSpaceVO;
import cn.yizhoucp.ump.biz.project.web.vo.RemoteConfigListResultVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 资源位管理后台旧流程
 *
 * <AUTHOR>
 */
@Deprecated
@Service
@Slf4j
public class AppAdSpaceAdminManager {

    @Resource
    AppAdSpaceJpaDAO appAdSpaceJpaDao;

    @Resource
    FeignLanlingService feignLanlingService;

    @Resource
    FeignProductService feignProductService;

    @Resource
    FeignChatieService feignChatieService;

    @Resource
    RedisManager redisManager;

    /**
     * 当前的环境 dev prod
     */
    @Value("${spring.profiles.active}")
    String env;

    private static final String HTTP_HEADER = "http://";
    private static final String HTTPS_HEADER = "https://";
    private static final Set SUPPORT_APP_ID_SET = Sets.newHashSet(ServicesAppIdEnum.lanling.getAppId(), ServicesAppIdEnum.chatie.getAppId());
    private static final String CHATIE_PROD = "awsprod";
    private static final String CHATIE_PRE = "awspre";
    //广告资源缓存 appId unionId
    private static final String ADVERTISING_RESOURCE = "advertising_resources:%s_%s";

    /**
     * 分页获取广告位信息
     *
     * @param appId        应用id
     * @param appAdSpaceVO 广告位结构体
     * @param page         当前页
     * @param size         每页天数
     * @return AdminPageVO<AppAdSpaceVO>
     */
    public AdminPageVO<AppAdSpaceVO> pageDetailInfo(Long appId, AppAdSpaceVO appAdSpaceVO, Integer page, Integer size) {
        log.debug("appId -> {}", appId);
        Pageable pageable = PageUtil.defaultPage(page - 1, size, "updateTime");
        Specification<AppAdSpaceDO> specification = this.getPageDetailInfoSql(appId, appAdSpaceVO);
        Page<AppAdSpaceDO> pageResult = appAdSpaceJpaDao.findAll(specification, pageable);
        if (CollectionUtils.isEmpty(pageResult.getContent())) {
            return new AdminPageVO<>(Lists.newArrayList(), page, size, Constant.FALSE, pageResult.getTotalElements(), pageResult.getTotalPages());
        }
        List<AppAdSpaceVO> appAdSpaceList = this.convertToVo(pageResult.getContent());
        boolean hasNext = page < pageResult.getTotalPages();
        return new AdminPageVO<>(appAdSpaceList, page, size, hasNext, pageResult.getTotalElements(), pageResult.getTotalPages());
    }

    /**
     * 更新应用广告信息
     *
     * @param appAdSpaceVo 请求参数
     * @param appId        应用id
     * @return CommonResultVO
     */
    public CommonResultVO updateAdSpace(AppAdSpaceVO appAdSpaceVo, Long appId) {
        log.debug("{}", JSON.toJSONString(appAdSpaceVo));
        CommonResultVO result = new CommonResultVO();
        if (EnvType.PROD.getEnv().equals(env) || EnvType.PRE.getEnv().equals(env)) {
            result.setMsg("请在 qa 环境操作后同步线上");
            return result;
        }
        // 校验广告位代码
        if (!this.checkAdCode(appAdSpaceVo, appId)) {
            result.setResult(false);
            result.setMsg("广告位代码不可重复");
            return result;
        }
        result.setResult(true);
        result.setMsg("操作成功");
        AppAdSpaceDO appAdSpace;
        if (null == appAdSpaceVo.getId()) {
            appAdSpace = new AppAdSpaceDO();
            appAdSpace.setAppId(appId);
            if (null == appAdSpaceVo.getType() && AdSpaceTypeEnum.splash.getCode().equals(appAdSpaceVo.getType())) {
                // 开屏页默认展示 4 秒
                appAdSpace.setDelay(4);
            }
            // 默认普通展示方式
            appAdSpace.setShowType(AdSpaceShowTypeEnum.normal);
        } else {
            Optional<AppAdSpaceDO> adOpt = appAdSpaceJpaDao.findById(appAdSpaceVo.getId());
            if (!adOpt.isPresent()) {
                result.setResult(false);
                result.setMsg("未找到对应配置信息");
                return result;
            }
            appAdSpace = adOpt.get();
            if (AdSpaceStatusEnum.put_on_ing.equals(appAdSpace.getStatus()) || AdSpaceStatusEnum.put_on.equals(appAdSpace.getStatus())) {
                result.setResult(false);
                result.setMsg("请先将配置下架再修改");
                return result;
            }
        }
        // 处理客户端传过来的时间格式
        Long startTime = this.getTimeFromAdminDate(appAdSpaceVo.getStartTime());
        Long endTime = this.getTimeFromAdminDate(appAdSpaceVo.getEndTime());
        // 校验是否有变更
        if (!this.checkUpdate(appAdSpaceVo, appAdSpace, startTime, endTime)) {
            result.setResult(false);
            result.setMsg("未做修改任何");
            return result;
        }
        // 内容更新
        this.updateAppAdSpace(appAdSpaceVo, appAdSpace, startTime, endTime);
        log.debug("save {}", JSON.toJSONString(appAdSpace));
        // 展示开始时间、结束时间
        appAdSpaceJpaDao.save(appAdSpace);
        return result;
    }

    /**
     * 处理后端传的日期字符串
     *
     * @param dateStr 日期字符串
     * @return Long
     */
    private Long getTimeFromAdminDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        Date beginDate;
        try {
            beginDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS Z").parse(dateStr.replace("Z", " UTC"));
        } catch (ParseException e) {
            throw new ServiceException(ErrorCode.LANLING_PARAM_ERROR, "请检查时间范围");
        }
        return beginDate.getTime();
    }

    /**
     * 校验广告位代码是否重复
     *
     * @param appAdSpaceVo 广告位信息
     * @param appId        应用id
     * @return boolean
     */
    private boolean checkAdCode(AppAdSpaceVO appAdSpaceVo, Long appId) {
        if (StringUtils.isBlank(appAdSpaceVo.getCode())) {
            return true;
        }
        List<AppAdSpaceDO> existCodeList = appAdSpaceJpaDao.findByAppIdAndCode(appId, appAdSpaceVo.getCode());
        if (null != appAdSpaceVo.getId()) {
            existCodeList = existCodeList.stream().filter(appAdSpace -> !appAdSpace.getId().equals(appAdSpaceVo.getId())).collect(Collectors.toList());
        }
        return CollectionUtils.isEmpty(existCodeList);
    }

    /**
     * 校验内容是否有变更
     *
     * @param appAdSpaceVo 广告位对象
     * @param appAdSpace   广告位实体
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return boolean true-有变更，false-无变更
     */
    private boolean checkUpdate(AppAdSpaceVO appAdSpaceVo, AppAdSpaceDO appAdSpace, Long startTime, Long endTime) {
        log.debug("{} {} {} {}", JSON.toJSONString(appAdSpaceVo), JSON.toJSONString(appAdSpace), startTime, endTime);
        boolean result = !Objects.equals(appAdSpaceVo.getCode(), appAdSpace.getCode())
                || !Objects.equals(appAdSpaceVo.getFileUrl(), appAdSpace.getFileUrl())
                || !Objects.equals(appAdSpaceVo.getRelationKey(), appAdSpace.getRelationKey())
                || !Objects.equals(AdSpaceTypeEnum.findByCode(appAdSpaceVo.getType()), appAdSpace.getType())
                || !Objects.equals(appAdSpaceVo.getWeight(), appAdSpace.getWeight())
                || !Objects.equals(appAdSpaceVo.getJumpUrl(), appAdSpace.getJumpUrl())
                || !Objects.equals(appAdSpaceVo.getDelay(), appAdSpace.getDelay())
                || !Objects.equals(appAdSpaceVo.getTimeInterval(), appAdSpace.getTimeInterval())
                || !Objects.equals(startTime, appAdSpace.getStartTime())
                || !Objects.equals(endTime, appAdSpace.getEndTime())
                || !Objects.equals(appAdSpaceVo.getJumpType(), appAdSpace.getJumpType().getCode())
                || !Objects.equals(appAdSpaceVo.getTargetEnv(), appAdSpace.getTargetEnv())
                || !Objects.equals(getSex(appAdSpaceVo.getSexList()), appAdSpace.getSex())
                || !Objects.equals(appAdSpaceVo.getBelongActivityCode(), appAdSpace.getBelongActivityCode());
        log.debug("result {}", result);
        return result;
    }

    /**
     * 更新广告信息
     *
     * @param appAdSpaceVo 广告位对象
     * @param appAdSpace   广告位实体
     * @param startTime    开始时间
     * @param endTime      结束时间
     */
    private void updateAppAdSpace(AppAdSpaceVO appAdSpaceVo, AppAdSpaceDO appAdSpace, Long startTime, Long endTime) {
        appAdSpace.setCode(appAdSpaceVo.getCode());
        appAdSpace.setFileUrl(appAdSpaceVo.getFileUrl());
        appAdSpace.setRelationKey(appAdSpaceVo.getRelationKey());
        appAdSpace.setType(AdSpaceTypeEnum.findByCode(appAdSpaceVo.getType()));
        appAdSpace.setWeight(appAdSpaceVo.getWeight());
        appAdSpace.setDelay(appAdSpaceVo.getDelay());
        appAdSpace.setTimeInterval(appAdSpaceVo.getTimeInterval());
        // 后续应该要加的条件，性别和展示类型（周期）
        appAdSpace.setSex(getSex(appAdSpaceVo.getSexList()));
//        appAdSpace.setShowType();
        appAdSpace.setPutOn(false);
        appAdSpace.setTargetEnv(appAdSpaceVo.getTargetEnv());
        appAdSpace.setStatus(AdSpaceStatusEnum.to_be_test);
        appAdSpace.setStartTime(startTime);
        appAdSpace.setEndTime(endTime);
        appAdSpace.setBelongActivityCode(appAdSpaceVo.getBelongActivityCode());
        // 跳转处理
        AdSpaceJumpTypeEnum jumpType = AdSpaceJumpTypeEnum.findByCode(appAdSpaceVo.getJumpType());
        appAdSpace.setJumpType(jumpType);
        appAdSpace.setJumpUrl(jumpType.getFullUrl(appAdSpaceVo.getJumpUrl(), appAdSpaceVo.getCode()));
    }

    /**
     * 根据类型和状态获取广告位信息
     *
     * @param appId        应用id
     * @param appAdSpaceVO 广告位资源
     * @return List<AppAdSpaceVO>
     */
    public List<AppAdSpaceVO> getDetailByType(Long appId, AppAdSpaceVO appAdSpaceVO) {
        AdminPageVO<AppAdSpaceVO> page = this.pageDetailInfo(appId, appAdSpaceVO, 0, 10000);
        return page.getItems();
    }

    /**
     * 开始测试处理
     *
     * @param id 配置id
     * @return CommonResultVO
     */
    public CommonResultVO startTest(Long id) {
        CommonResultVO result = new CommonResultVO();
        result.setResult(false);
        if (EnvType.PROD.getEnv().equals(env) || EnvType.PRE.getEnv().equals(env) || EnvType.CHATIE_PRE.getEnv().equals(env) || EnvType.CHATIE_PROD.getEnv().equals(env)) {
            result.setMsg("请在 qa 环境操作后同步线上");
            return result;
        }
        AppAdSpaceDO appAdSpace;
        try {
            appAdSpace = appAdSpaceJpaDao.findById(id).orElseThrow(() -> new ServiceException(ErrorCode.SYSTEM_EXCEPTION, "未找到对应配置信息"));
        } catch (ServiceException e) {
            result.setMsg(e.getErrorMsg());
            return result;
        }
        if (AdSpaceStatusEnum.put_on_ing.equals(appAdSpace.getStatus()) || AdSpaceStatusEnum.put_on.equals(appAdSpace.getStatus())) {
            result.setMsg("配置已上架");
            return result;
        }
        if (AdSpaceStatusEnum.testing.equals(appAdSpace.getStatus())) {
            result.setMsg("当前配置测试中，请勿重复操作");
            return result;
        }
        if (StringUtils.isBlank(appAdSpace.getCode()) || null == appAdSpace.getType()
                || null == appAdSpace.getStartTime() || null == appAdSpace.getEndTime()) {
            result.setMsg("缺少必要配置，请检查后重试");
            return result;
        }
        if (AdSpaceTypeEnum.gift_banner.equals(appAdSpace.getType())) {
            Boolean updateResult = this.updateGiftBanner(appAdSpace);
            result.setResult(updateResult);
            result.setMsg(updateResult ? "操作成功" : "刷新礼物banner配置失败");
            if (updateResult) {
                appAdSpace.setStatus(AdSpaceStatusEnum.testing);
                appAdSpaceJpaDao.save(appAdSpace);
            }
            return result;
        }
        long curTime = System.currentTimeMillis();
        AdSpaceTypeEnum type = appAdSpace.getType();
        List<AppAdSpaceDO> allList = appAdSpaceJpaDao.findByAppIdAndTypeAndStatus(appAdSpace.getAppId(), type, AdSpaceStatusEnum.testing);
        allList.add(appAdSpace);
        List<AppAdSpaceDO> adList = appAdSpaceJpaDao.findByAppIdAndTypeAndPutOnAndCurTime(appAdSpace.getAppId(), type, true, curTime);
        allList.addAll(adList);
        // 按权重排序
        allList = allList.stream().sorted(Comparator.comparing(AppAdSpaceDO::getWeight).reversed()).collect(Collectors.toList());
        String configStr = this.buildConfigStr(allList, type);
        AdminRemoteConfigVO config = new AdminRemoteConfigVO();
        config.setKey(type.getRemoteConfigKey());
        config.setValue(configStr);
        log.debug(" {}", JSON.toJSONString(config));
        if (appAdSpace.getAppId().equals(ServicesAppIdEnum.lanling.getAppId())) {
            Boolean updateResult = feignLanlingService.updateRemoteConfig(config).successData();
            result.setResult(updateResult);
            if (updateResult) {
                result.setMsg("操作成功");
                appAdSpace.setStatus(AdSpaceStatusEnum.testing);
                appAdSpaceJpaDao.save(appAdSpace);
                feignLanlingService.refreshCacheRemoteConfig();
            } else {
                result.setMsg("更新 remote config 失败");
            }
        }

        if (appAdSpace.getAppId().equals(ServicesAppIdEnum.chatie.getAppId())) {
            Boolean updateResult = feignChatieService.updateRemoteConfig(config).successData();
            result.setResult(updateResult);
            if (updateResult) {
                result.setMsg("操作成功");
                appAdSpace.setStatus(AdSpaceStatusEnum.testing);
                appAdSpaceJpaDao.save(appAdSpace);
                feignChatieService.refreshCacheRemoteConfig();
            } else {
                result.setMsg("更新 remote config 失败");
            }
        }

        return result;
    }


    /**
     * 获取分页查询动态 sql
     *
     * @param appAdSpaceVO 广告位资源
     * @return Specification<AppAdSpaceDO>
     */
    private Specification<AppAdSpaceDO> getPageDetailInfoSql(Long appId, AppAdSpaceVO appAdSpaceVO) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("appId"), appId));
            if (StringUtils.isNotBlank(appAdSpaceVO.getType())) {
                predicates.add(criteriaBuilder.equal(root.get("type"), AdSpaceTypeEnum.findByCode(appAdSpaceVO.getType())));
            }
            if (StringUtils.isNotBlank(appAdSpaceVO.getBelongActivityCode())) {
                predicates.add(criteriaBuilder.equal(root.get("belongActivityCode"), appAdSpaceVO.getBelongActivityCode()));
            }
            if (StringUtils.isNotBlank(appAdSpaceVO.getStatus())) {
                predicates.add(criteriaBuilder.equal(root.get("status"), AdSpaceStatusEnum.findByCode(appAdSpaceVO.getStatus())));
            } else {
                predicates.add(criteriaBuilder.notEqual(root.get("status"), AdSpaceStatusEnum.deleted));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * do 转 vo
     *
     * @param appAdSpaceList 广告位集合
     * @return List<AppAdSpaceVO>
     */
    private List<AppAdSpaceVO> convertToVo(List<AppAdSpaceDO> appAdSpaceList) {
        if (CollectionUtils.isEmpty(appAdSpaceList)) {
            return null;
        }
        List<AppAdSpaceVO> result = new ArrayList<>();
        for (AppAdSpaceDO appAdSpace : appAdSpaceList) {
            AppAdSpaceVO appAdSpaceVo = new AppAdSpaceVO();
            BeanUtils.copyProperties(appAdSpace, appAdSpaceVo);
            appAdSpaceVo.setType(appAdSpace.getType() == null ? null : appAdSpace.getType().getCode());
            appAdSpaceVo.setPutOn(Objects.isNull(appAdSpace.getPutOn()) ? -1 : appAdSpace.getPutOn() ? 1 : 0);
            appAdSpaceVo.setStatus(appAdSpace.getStatus().getCode());
            appAdSpaceVo.setStartTime(DateUtil.format(new Date(appAdSpace.getStartTime()), DateUtil.YMDHMS));
            appAdSpaceVo.setEndTime(DateUtil.format(new Date(appAdSpace.getEndTime()), DateUtil.YMDHMS));
            appAdSpaceVo.setJumpType(appAdSpace.getJumpType().getCode());
            appAdSpaceVo.setSexList(Optional.ofNullable(appAdSpace.getSex()).map(SexType::getCode).orElse(null));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ctime", DateUtil.format(appAdSpace.getCreateTime(), DateUtil.YMDHMS));
            jsonObject.put("url", appAdSpace.getFileUrl());
            if (StringUtils.isNotBlank(appAdSpace.getFileUrl())) {
                jsonObject.put("type", appAdSpace.getFileUrl().contains(".svga") ? "svga" : "image");
            }
            List<Object> contentToHtml = new ArrayList<>();
            contentToHtml.add(jsonObject);
            appAdSpaceVo.setContentToHtml(contentToHtml);
            result.add(appAdSpaceVo);
        }
        return result;
    }

    /**
     * 上架或者下架广告位信息
     *
     * @param appId 应用id
     */
    public void putOnOrOffAdSpace(Long appId) {
        long curTime = System.currentTimeMillis();
        List<AppAdSpaceDO> needPutOnList = appAdSpaceJpaDao.findByAppIdAndPutOnAndStatusAndStartTime(appId, true,
                AdSpaceStatusEnum.put_on_ing, curTime);
        log.info("needPutOnList {}", JSON.toJSONString(needPutOnList));
        List<AppAdSpaceDO> needPutOffList = appAdSpaceJpaDao.findByAppIdAndStatusAndEndTime(appId,
                AdSpaceStatusEnum.put_on, curTime);
        log.info("needPutOffList {}", JSON.toJSONString(needPutOffList));
        if (!CollectionUtils.isEmpty(needPutOffList)) {
            needPutOnList.addAll(needPutOffList);
        }
        if (CollectionUtils.isEmpty(needPutOnList)) {
            log.info("没有需要更新的广告位信息");
            return;
        }
        List<AdSpaceTypeEnum> successTypeList = new ArrayList<>();
        // 礼物 banner 处理
        List<AppAdSpaceDO> giftBannerList = needPutOnList.stream().filter(appAdSpace -> AdSpaceTypeEnum.gift_banner.equals(appAdSpace.getType())).collect(Collectors.toList());
        boolean giftResult = this.syncGiftBanner(giftBannerList);
        if (giftResult) {
            successTypeList.add(AdSpaceTypeEnum.gift_banner);
        }
        needPutOnList.removeAll(giftBannerList);
        List<AdSpaceTypeEnum> needRefreshTypeList = needPutOnList.stream().map(AppAdSpaceDO::getType).distinct().collect(Collectors.toList());
        log.info("需要更新广告位信息 {}", JSON.toJSONString(needRefreshTypeList));
        for (AdSpaceTypeEnum type : needRefreshTypeList) {
            Boolean result = this.refreshAdSpaceAction(appId, type, curTime);
            if (result) {
                successTypeList.add(type);
            }
        }
        log.info("成功更新的广告位 {}", JSON.toJSONString(successTypeList));
        // 更新状态
        appAdSpaceJpaDao.updatePutOnByAppIdAndPutOnAndCurTimeAndTypeIn(appId, AdSpaceStatusEnum.put_on, true, curTime, successTypeList);
        appAdSpaceJpaDao.updatePutOffByAppIdAndPutOnAndCurTimeAndTypeIn(appId, AdSpaceStatusEnum.put_off, true, curTime, successTypeList);
        Boolean refreshResult = false;
        if (ServicesAppIdEnum.chatie.getAppId().equals(appId)) {
            refreshResult = feignChatieService.refreshCacheRemoteConfig().successData();
        } else {
            refreshResult = feignLanlingService.refreshCacheRemoteConfig().successData();
        }
        log.info("刷新 remote config 结果 {}", refreshResult);
    }

    /**
     * 刷新 remote config
     *
     * @param appId   应用id
     * @param type    广告为类型
     * @param curTime 当前时间
     * @return Boolean
     */
    private Boolean refreshAdSpaceAction(Long appId, AdSpaceTypeEnum type, long curTime) {
        if (!SUPPORT_APP_ID_SET.contains(appId)) {
            return false;
        }

        // 查询展示资源
        List<AppAdSpaceDO> adList = appAdSpaceJpaDao.findByAppIdAndTypeAndPutOnAndCurTime(appId, type, true, curTime);

        // 开发、测试环境使用默认 key
        if (EnvType.TEST.getEnv().equals(env) || EnvType.DEV.getEnv().equals(env)) {
            return testRefreshHandle(appId, type, adList);
        } else if (EnvType.PRE.getEnv().equals(env) || EnvType.PROD.getEnv().equals(env) || CHATIE_PRE.equals(env) || CHATIE_PROD.equals(env)) {
            return prodRefreshHandle(appId, type, adList);
        } else {
            log.error("RemoteConfig 更新环境异常");
        }
        return false;
    }

    private boolean testRefreshHandle(Long appId, AdSpaceTypeEnum type, List<AppAdSpaceDO> adList) {
        String configStr = this.buildConfigStr(adList, type);
        AdminRemoteConfigVO config = new AdminRemoteConfigVO();
        config.setKey(type.getRemoteConfigKey());
        config.setValue(configStr);
        if (ServicesAppIdEnum.lanling.getAppId().equals(appId)) {
            feignLanlingService.updateRemoteConfig(config).successData();
        }
        if (ServicesAppIdEnum.chatie.getAppId().equals(appId)) {
            feignChatieService.updateRemoteConfig(config).successData();
        }
        return true;
    }

    private boolean prodRefreshHandle(Long appId, AdSpaceTypeEnum type, List<AppAdSpaceDO> adList) {
        // 按环境分组
        Map<String, List<AppAdSpaceDO>> adGroups = adList.stream().collect(Collectors.groupingBy(AppAdSpaceDO::getTargetEnv));
        // 生产自动上预发
        adsGroupHandle(adGroups);
        log.info("待更新广告位资源 adGroups:{}", JSONObject.toJSONString(adGroups));

        // 根据分组采取更新预发与生产 RemoteConfig
        AdminRemoteConfigVO config = new AdminRemoteConfigVO();
        for (Map.Entry<String, List<AppAdSpaceDO>> adGroup : adGroups.entrySet()) {
            String configStr = buildConfigStr(adGroup.getValue(), type);
            config.setKey(getRemoteConfigKeyWithEnv(adGroup.getKey(), type.getRemoteConfigKey()));
            config.setValue(configStr);
            if (ServicesAppIdEnum.lanling.getAppId().equals(appId)) {
                log.info("更新 remoteConfig:{}", JSONObject.toJSONString(config));
                feignLanlingService.updateRemoteConfig(config).successData();
            }
            if (ServicesAppIdEnum.chatie.getAppId().equals(appId)) {
                log.info("更新 remoteConfig:{}", JSONObject.toJSONString(config));
                feignChatieService.updateRemoteConfig(config).successData();
            }
        }
        return true;
    }

    private Map<String, List<AppAdSpaceDO>> adsGroupHandle(Map<String, List<AppAdSpaceDO>> adGroups) {
        List<AppAdSpaceDO> preAds = Optional.ofNullable(adGroups.get(EnvType.PRE.getEnv())).orElse(Lists.newArrayList());
        List<AppAdSpaceDO> prodAds = Optional.ofNullable(adGroups.get(EnvType.PROD.getEnv())).orElse(Lists.newArrayList());
        preAds.addAll(prodAds);
        adGroups.put(EnvType.PROD.getEnv(), prodAds);
        adGroups.put(EnvType.PRE.getEnv(), preAds);
        return adGroups;
    }

    private String getRemoteConfigKeyWithEnv(String env, String remoteConfigKey) {
        if (EnvType.PRE.getEnv().equals(env)) {
            return AdSpaceTypeEnum.PRE_PREFIX + remoteConfigKey;
        }
        return remoteConfigKey;
    }

    /**
     * 构建配置
     *
     * @param adList 广告位配置集合
     * @param type   广告位类型
     * @return String
     */
    private String buildConfigStr(List<AppAdSpaceDO> adList, AdSpaceTypeEnum type) {
        if (AdSpaceTypeEnum.splash.equals(type)) {
            if (CollectionUtils.isEmpty(adList) || adList.size() > 1) {
                log.warn("开屏页为空或者配置多个 {}", JSON.toJSONString(adList));
                return "{}";
            }
            return JSON.toJSONString(this.buildSplashProperty(adList.get(0)));
        } else {
            if (CollectionUtils.isEmpty(adList)) {
                return "[]";
            }
            List<ActivityBannerProperty> bannerList = new ArrayList<>();
            for (AppAdSpaceDO appAdSpace : adList) {
                bannerList.add(this.buildBannerProperty(appAdSpace));
            }
            return JSON.toJSONString(bannerList);
        }
    }

    /**
     * 构建开屏页对象
     *
     * @param appAdSpace 广告位信息
     * @return SplashProperty
     */
    private SplashProperty buildSplashProperty(AppAdSpaceDO appAdSpace) {
        SplashProperty splash = new SplashProperty();
        splash.setUrl(appAdSpace.getJumpUrl());
        splash.setImage(appAdSpace.getFileUrl());
        splash.setDelay(appAdSpace.getDelay());
        splash.setInterval(appAdSpace.getTimeInterval());
        splash.setStartTime(appAdSpace.getStartTime() / 1000);
        splash.setEndTime(appAdSpace.getEndTime() / 1000);
        return splash;
    }

    /**
     * 构建 banner 对象
     *
     * @param appAdSpace 广告位信息
     * @return ActivityBannerProperty
     */
    private ActivityBannerProperty buildBannerProperty(AppAdSpaceDO appAdSpace) {
        ActivityBannerProperty bannerProperty = new ActivityBannerProperty();
        bannerProperty.setActivityName(appAdSpace.getCode());
        bannerProperty.setActivityCode(appAdSpace.getCode());
        bannerProperty.setActivityIcon(appAdSpace.getFileUrl());
        bannerProperty.setActivityUrl(appAdSpace.getJumpUrl());
        bannerProperty.setActivityIsShow("true");
        bannerProperty.setShowType(appAdSpace.getShowType().getCode());
        bannerProperty.setStartTime(appAdSpace.getStartTime());
        bannerProperty.setEndTime(appAdSpace.getEndTime());
        bannerProperty.setPermission(getPermission(appAdSpace.getSex(),
                appAdSpace.getJoinBigRCallBackActivity(),
                appAdSpace.getJoinLuckyBagCallBackActivity(),
                appAdSpace.getJoinGoddessTrainActivity()));
        if (AdSpaceTypeEnum.single_chat_buoy.equals(appAdSpace.getType()) || AdSpaceTypeEnum.family_buoy.equals(appAdSpace.getType())
                || AdSpaceTypeEnum.chat_room_buoy.equals(appAdSpace.getType()) || AdSpaceTypeEnum.live_broadcasting_room_buoy.equals(appAdSpace.getType())
                || AdSpaceTypeEnum.home_page_buoy.equals(appAdSpace.getType()) || AdSpaceTypeEnum.voice_chat_room_buoy.equals(appAdSpace.getType())) {
            bannerProperty.setActivitySvgaKey(appAdSpace.getRelationKey());
        }
        if (null != appAdSpace.getSex()) {
            bannerProperty.setSex(appAdSpace.getSex().getCode());
        }
        return bannerProperty;
    }

    /**
     * 同步礼物 banner
     *
     * @param giftBannerList 广告位信息集合
     */
    private boolean syncGiftBanner(List<AppAdSpaceDO> giftBannerList) {
        boolean result = false;
        if (!CollectionUtils.isEmpty(giftBannerList)) {
            for (AppAdSpaceDO appAdSpace : giftBannerList) {
                // 礼物 icon 直接切在背景图中
                Boolean giftResult = this.updateGiftBanner(appAdSpace);
                if (giftResult) {
                    result = true;
                    log.debug("更新礼物 banner 成功 {} {}", appAdSpace.getId(), appAdSpace.getRelationKey());
                } else {
                    log.debug("更新礼物 banner 失败 {} {}", appAdSpace.getId(), appAdSpace.getRelationKey());
                }
            }
        }
        return result;
    }

    /**
     * 更新礼物banner 配置
     *
     * @param appAdSpace 配置
     * @return Boolean
     */
    private Boolean updateGiftBanner(AppAdSpaceDO appAdSpace) {
        GiftBannerModel model = new GiftBannerModel();
        model.setJumpUrl(appAdSpace.getJumpUrl());
        model.setBackgroundImg(appAdSpace.getFileUrl());
        model.setStartTime(appAdSpace.getStartTime());
        model.setEndTime(appAdSpace.getEndTime());
        return feignProductService.updateGiftBannerByKey(appAdSpace.getAppId(), appAdSpace.getRelationKey(), JSON.toJSONString(model)).successData();
    }

    /**
     * 直接下架配置
     *
     * @param id 配置id
     * @return CommonResultVO
     */
    public CommonResultVO putOffAdSpace(Long id) {
        CommonResultVO result = new CommonResultVO();
        if (EnvType.PROD.getEnv().equals(env) || EnvType.PRE.getEnv().equals(env) || EnvType.CHATIE_PRE.getEnv().equals(env) || EnvType.CHATIE_PROD.getEnv().equals(env)) {
            result.setResult(false);
            result.setMsg("请在 qa 环境操作后同步线上");
            return result;
        }
        Optional<AppAdSpaceDO> adOpt = appAdSpaceJpaDao.findById(id);
        if (!adOpt.isPresent()) {
            result.setResult(false);
            result.setMsg("未找到对应配置");
            return result;
        }
        AppAdSpaceDO appAdSpace = adOpt.get();
        //if (!AdSpaceStatusEnum.put_on.equals(appAdSpace.getStatus()) && !AdSpaceStatusEnum.put_on_ing.equals(appAdSpace.getStatus())) {
        //    result.setResult(false);
        //    result.setMsg("当前配置未上架");
        //    return result;
        //}
        Long putOffTime = appAdSpace.getPutOffTime();
        appAdSpace.setPutOffTime(System.currentTimeMillis() - 5000);
        if (AdSpaceTypeEnum.gift_banner.equals(appAdSpace.getType())) {
            appAdSpace.setEndTime(System.currentTimeMillis());
            Boolean refreshResult = this.updateGiftBanner(appAdSpace);
            if (refreshResult) {
                appAdSpace.setStatus(AdSpaceStatusEnum.put_off);
                appAdSpaceJpaDao.save(appAdSpace);
                result.setResult(true);
                result.setMsg("操作成功");
            } else {
                result.setMsg("下架失败，请稍后重试");
            }
            return result;
        }
        appAdSpace.setStatus(AdSpaceStatusEnum.put_off);
        appAdSpaceJpaDao.save(appAdSpace);
        this.refreshAdSpaceAction(appAdSpace.getAppId(), appAdSpace.getType(), System.currentTimeMillis());
        Boolean refreshResult = false;
        if (ServicesAppIdEnum.chatie.getCode().equals(appAdSpace.getAppId())) {
            refreshResult = feignChatieService.refreshCacheRemoteConfig().successData();
        } else {
            refreshResult = feignLanlingService.refreshCacheRemoteConfig().successData();
        }
        // 刷新配置失败，重置状态
        if (!refreshResult) {
            appAdSpace.setPutOffTime(putOffTime);
            appAdSpace.setStatus(AdSpaceStatusEnum.put_on);
            appAdSpaceJpaDao.save(appAdSpace);
            result.setResult(false);
            result.setMsg("下架失败，请稍后重试");
            return result;
        }
        result.setResult(true);
        result.setMsg("操作成功");
        return result;
    }

    /**
     * 删除配置
     *
     * @param id 配置id
     * @return CommonResultVO
     */
    public CommonResultVO deleteAdSpace(Long id) {
        CommonResultVO result = new CommonResultVO();
        if (EnvType.PROD.getEnv().equals(env) || EnvType.PRE.getEnv().equals(env) || EnvType.CHATIE_PRE.getEnv().equals(env) || EnvType.CHATIE_PROD.getEnv().equals(env)) {
            result.setResult(false);
            result.setMsg("请在 qa 环境操作后同步线上");
            return result;
        }
        Optional<AppAdSpaceDO> adOpt = appAdSpaceJpaDao.findById(id);
        if (!adOpt.isPresent()) {
            result.setResult(false);
            result.setMsg("未找到对应配置");
            return result;
        }
        AppAdSpaceDO appAdSpace = adOpt.get();
        if (AdSpaceStatusEnum.put_on_ing.equals(appAdSpace.getStatus()) || AdSpaceStatusEnum.put_on.equals(appAdSpace.getStatus())) {
            result.setResult(false);
            result.setMsg("请先将配置下架");
            return result;
        }
        appAdSpace.setStatus(AdSpaceStatusEnum.deleted);
        appAdSpaceJpaDao.save(appAdSpace);
        result.setResult(true);
        result.setMsg("操作成功");
        return result;
    }

    /**
     * 根据所属活动获取资源列表
     *
     * @param belongActivityCode
     * @return java.util.List<api.project.dal.jpa.dataobject.AppAdSpaceDO>
     */
    public List<AppAdSpaceDO> getListByBelongActivityCode(String belongActivityCode) {
        return appAdSpaceJpaDao.findByBelongActivityCode(belongActivityCode, Lists.newArrayList(AdSpaceStatusEnum.put_on, AdSpaceStatusEnum.put_on_ing, AdSpaceStatusEnum.testing));
    }

    private List<String> getPermission(SexType sex,
                                       Boolean joinBigRCallBackActivity,
                                       Boolean joinLuckyBagCallBackActivity,
                                       Boolean joinGoddessTrainActivity) {
        log.info("debug-joinBigRCallBackActivity:{}, joinBigRCallBackActivity:{}", joinBigRCallBackActivity, joinLuckyBagCallBackActivity);
        UserPermission userPermission = new UserPermission();
        UserVO userVO = new UserVO();
        Boolean needPermission = Boolean.FALSE;
        if (Objects.nonNull(sex)) {
            userVO.setSex(sex);
            needPermission = Boolean.TRUE;
        }
        if (Boolean.TRUE.equals(joinLuckyBagCallBackActivity)) {
            userVO.setJoinLuckyBagCallBackActivity(joinLuckyBagCallBackActivity);
            needPermission = Boolean.TRUE;
        }
        if (Boolean.TRUE.equals(joinBigRCallBackActivity)) {
            userVO.setJoinBigRCallBackActivity(joinBigRCallBackActivity);
            needPermission = Boolean.TRUE;
        }
        if (Boolean.TRUE.equals(joinGoddessTrainActivity)) {
            userVO.setJoinGoddessTrainActivity(joinGoddessTrainActivity);
            needPermission = Boolean.TRUE;
        }
        if (needPermission) {
            userPermission.addCondition(userVO);
            log.info("debug-permission:{}, long:{}, user:{}", JSON.toJSONString(userVO), userPermission.toLongValue().toString(), JSON.toJSONString(userVO));
            return Lists.newArrayList(userPermission.toLongValue().toString());
        }
        return Lists.newArrayList();
    }

    private SexType getSex(String sexList) {
        // 未传或传两个性别等于无限制
        if (StringUtils.isBlank(sexList)) {
            return null;
        }
        String[] sex = sexList.split(",");
        if (sex.length == 2) {
            return null;
        }
        return SexType.convertFormCode(sex[0]);
    }

    /**
     * 获取广告资源接口
     *
     * @param appId   appId
     * @param unionId unionId
     * @return RemoteConfigListResultVO
     */
    public RemoteConfigListResultVO getAdResource(Long appId, String unionId) {
        log.info("AppAdSpaceManager-getAdResource appId : {} unionId : {}", appId, unionId);
        return this.getAdResourceOrRefreshCache(appId, unionId, false);
    }

    /**
     * 获取或者刷新资源广告位
     *
     * @param appId        appId
     * @param unionId      unionId
     * @param refreshCache 是否刷新缓存
     * @return
     */
    private RemoteConfigListResultVO getAdResourceOrRefreshCache(Long appId, String unionId, Boolean refreshCache) {
        log.info("AppAdSpaceManager-getAdResourceOrReFreshCache appId : {} unionId : {} refreshCache : {}", appId, unionId, refreshCache);
        RemoteConfigListResultVO remoteConfigListResultVO = new RemoteConfigListResultVO();
        if (Objects.isNull(appId) || StringUtils.isBlank(unionId)) {
            return remoteConfigListResultVO;
        }

        //处理 pre和线上
        String key = String.format(ADVERTISING_RESOURCE, appId, unionId);
        if (EnvType.PRE.getEnv().equals(env)) {
            key = AdSpaceTypeEnum.PRE_PREFIX.replace(".", "_") + key;
        }
        if (!refreshCache) {
            Object o = redisManager.get(key);
            if (Objects.nonNull(o)) {
                String configs = o.toString();
                JSONObject jsonObject = JSON.parseObject(configs);
                String hashString = MD5.getMD5(configs);
                remoteConfigListResultVO.setConfigs(jsonObject);
                remoteConfigListResultVO.setHash(hashString);
                return remoteConfigListResultVO;
            }
        }

        long curTime = System.currentTimeMillis();
        List<AppAdSpaceDO> adList = appAdSpaceJpaDao.findListByAppIdAndPutOnAndCurTime(appId, 1, curTime);
        // List<AppAdSpaceDO> adList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adList)) {
            return remoteConfigListResultVO;
        }

        JSONObject voConfig = new JSONObject();
        // 开发、测试环境使用默认 key
        if (EnvType.PRE.getEnv().equals(env) || EnvType.PROD.getEnv().equals(env) || CHATIE_PRE.equals(env) || CHATIE_PROD.equals(env)) {
            voConfig = getAdResourceList(appId, adList);
        } else {
            voConfig = getTestAdResourceList(appId, adList);
        }
        String value = voConfig.toJSONString();
        redisManager.set(key, value, 6 * DateUtil.ONE_MINUTE_SECONDS);
        remoteConfigListResultVO.setConfigs(voConfig);
        remoteConfigListResultVO.setHash(MD5Util.encode(value));
        return remoteConfigListResultVO;
    }

    /**
     * 刷新广告位缓存
     */
    public Boolean refreshAdResource() {
        if (!redisManager.setnx("refresh_ad_resource_no_repeat", 1, 3)) {
            return false;
        }
        log.info("AppAdSpaceManager-refreshAdResource into");
        List<ServicesAppIdEnum> listGroupByUnionId = ServicesAppIdEnum.getListGroupByUnionId();
        for (ServicesAppIdEnum appIdEnum : listGroupByUnionId) {
            //海外暂时不处理
            if (ServicesAppIdEnum.chatie.equals(appIdEnum)) {
                continue;
            }
            this.getAdResourceOrRefreshCache(appIdEnum.getAppId(), appIdEnum.getUnionId(), true);
        }
        return true;
    }

    /**
     * 获取测试的资源列表
     *
     * @param appId  appId
     * @param adList banner列表
     * @return JSONObject
     */
    private JSONObject getTestAdResourceList(Long appId, List<AppAdSpaceDO> adList) {
        Map<AdSpaceTypeEnum, List<AppAdSpaceDO>> adGroups = adList.stream().filter(e -> e.getType() != null).collect(Collectors.groupingBy(AppAdSpaceDO::getType));
        //输出AdSpaceTypeEnum所有数据，如果没有则为[]
        JSONObject result = new JSONObject();
        for (AdSpaceTypeEnum type : AdSpaceTypeEnum.values()) {
            if (AdSpaceTypeEnum.gift_banner.equals(type)) {
                continue;
            }
            List<AppAdSpaceDO> item = adGroups.getOrDefault(type, null);
            String s = this.buildConfigStr(item, type);
            result.put(type.getRemoteConfigKey(), AdSpaceTypeEnum.splash.equals(type) ? JSON.parseObject(s) : JSON.parseArray(s));
        }
        return result;
    }

    /**
     * 获取预发线上的资源列表
     *
     * @param appId  appId
     * @param adList banner列表
     * @return JSONObject
     */
    private JSONObject getAdResourceList(Long appId, List<AppAdSpaceDO> adList) {
        JSONObject result = new JSONObject();
        Map<AdSpaceTypeEnum, List<AppAdSpaceDO>> adTypeGroups = adList.stream().collect(Collectors.groupingBy(AppAdSpaceDO::getType));

        for (AdSpaceTypeEnum type : AdSpaceTypeEnum.values()) {
            if (AdSpaceTypeEnum.gift_banner.equals(type)) {
                continue;
            }
            List<AppAdSpaceDO> item = adTypeGroups.getOrDefault(type, null);
            if (CollectionUtils.isEmpty(item)) {
                result.put(type.getRemoteConfigKey(), AdSpaceTypeEnum.splash.equals(type) ? JSON.parseObject("{}") : JSON.parseArray("[]"));
            } else {
                // 按环境分组
                Map<String, List<AppAdSpaceDO>> adGroups = item.stream().collect(Collectors.groupingBy(AppAdSpaceDO::getTargetEnv));
                // 生产自动上预发
                adsGroupHandle(adGroups);
                AdminRemoteConfigVO config = new AdminRemoteConfigVO();
                for (Map.Entry<String, List<AppAdSpaceDO>> adGroup : adGroups.entrySet()) {
                    if (!adGroup.getKey().equals(env)) {
                        continue;
                    }
                    String s = buildConfigStr(adGroup.getValue(), type);
                    String key = getRemoteConfigKeyWithEnv(adGroup.getKey(), type.getRemoteConfigKey());
                    result.put(key, AdSpaceTypeEnum.splash.equals(type) ? JSON.parseObject(s) : JSON.parseArray(s));
                }
            }
        }
        return result;
    }

}
