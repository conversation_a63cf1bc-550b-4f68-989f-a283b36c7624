package cn.yizhoucp.ump.biz.project.biz.manager.activity.eternalLove;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.coinservices.IntimacyUserInfoVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBasicVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogItemVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogTimeItemVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.FamilyRankVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.FlowerGodTokenExchangeResult;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.FlowerGodVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.StageVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.TeamVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.ThanksgivingBattleConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawLogJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.BROADCAST;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.CUR_FLOWER_GOD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FAMILY_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_DRAW;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_TAKE_PRIZE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_TOKEN;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GIRL_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GLOBAL_FLOWER_GOD_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GROWTH_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.HEAD_FRAME_RECEIVE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.RELATIONSHIP_FORM;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.TASK_TAKE_PRIZE;

@Service
@Slf4j
public class EternalLoveIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private EternalLoveBizManager eternalLoveBizManager;
    @Resource
    private EternalLoveRankManager eternalLoveRankManager;
    @Resource
    private FeignSnsService feignSnsService;
    @Resource
    private DrawLogJpaDAO drawLogJpaDAO;
    @Resource
    private EternalLoveDrawManager eternalLoveDrawManager;
    @Resource
    private EternalLoveTrackManager eternalLoveTrackManager;

    @Override
    public IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        if (toUid == null) {
            toUid = this.getCpByIntimacy(param);
        }

        IndexVO indexVO = new IndexVO();
        indexVO.setStage(eternalLoveBizManager.stage());
        indexVO.setGirlRank(eternalLoveRankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(String.format(GIRL_RANK, indexVO.getStage()))
                        .type(RankContext.RankType.user)
                        .param(param)
                        .supportDiff(Boolean.TRUE)
                        .build()));
        indexVO.setGlobalFlowerValue(Optional.ofNullable(redisManager.getLong(GLOBAL_FLOWER_GOD_VALUE)).orElse(0L) * 100 / GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT);
        indexVO.setHeadFrameReceive(Boolean.TRUE.equals(redisManager.hasKey(String.format(HEAD_FRAME_RECEIVE, param.getUid()))));
        indexVO.setFlowerGod(confirmFriend(param, toUid));
        indexVO.setFlowerGodToken(Optional.ofNullable(redisManager.getInteger(String.format(FLOWER_GOD_TOKEN, param.getUid()))).orElse(0));
        indexVO.setBroadcast(Optional.ofNullable(redisManager.lGet(BROADCAST, 0, 60)).orElse(Collections.emptyList()));
        indexVO.setMyFlowerValue(Optional.ofNullable(redisManager.getInteger(String.format(FLOWER_GOD_VALUE, param.getUid()))).orElse(0));

        UserVO userVO = feignUserService.getBasic(param.getUid(), param.getAppId()).successData();
        if (userVO != null && userVO.getSex() != null) {
            indexVO.setSex(userVO.getSex().getCode());
        }

        indexVO.setTask(TaskVO.builder().taskItemList(Lists.newArrayListWithCapacity(7)).build());
        for (EternalLoveConstant.Task task : EternalLoveConstant.Task.values()) {
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskCode(task.name());
            taskItem.setCurFinishTimes(Math.min(task.getFinishTimes(), Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), DateUtil.getNowYyyyMMdd(), task.name()))).orElse(0)));
            int buttonStatus;
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(TASK_TAKE_PRIZE, param.getUid(), DateUtil.getNowYyyyMMdd(), taskItem.getTaskCode())))) {
                buttonStatus = -1;
            } else if (taskItem.getCurFinishTimes() < task.getFinishTimes()) {
                buttonStatus = 0;
            } else {
                buttonStatus = 1;
            }
            taskItem.setButtonStatus(buttonStatus);
            indexVO.getTask().getTaskItemList().add(taskItem);
        }

        FamilyRankVO familyRankVO = new FamilyRankVO();
        familyRankVO.setStage1(eternalLoveRankManager.getRank(RankContext.builder()
                .rankKey(String.format(FAMILY_RANK, 1))
                .param(param)
                .activityCode(ACTIVITY_CODE)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.family)
                .build()));
        if (familyRankVO.getStage1() != null && familyRankVO.getStage1().getMyselfRank() == null && userVO != null) {
            familyRankVO.getStage1().setMyselfRank((RankItem.builder().rank(-1L).name(userVO.getName()).icon(userVO.getAvatar()).value(0L).build()));
        }
        if (indexVO.getStage() >= 2) {
            StageVO stage2 = new StageVO();
            stage2.setRank(eternalLoveRankManager.getRank(RankContext.builder()
                    .rankKey(String.format(FAMILY_RANK, 2))
                    .param(param)
                    .activityCode(ACTIVITY_CODE)
                    .type(RankContext.RankType.family)
                    .build()));
//            if (familyRankVO.getStage1() != null && familyRankVO.getStage1().getMyselfRank() == null && userVO != null) {
//                familyRankVO.getStage1().setMyselfRank((RankItem.builder().rank(-1L).name(userVO.getName()).icon(userVO.getAvatar()).value(0L).build()));
//            }
            List<List<TeamVO>> teamListList = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                teamListList.add(new ArrayList<>());
            }
            List<RankItem> rankList = familyRankVO.getStage1().getRankList();
            Map<Long, Long> collect = stage2.getRank().getRankList().stream().collect(Collectors.toMap(RankItem::getId, RankItem::getValue, (oldValue, newValue) -> oldValue));

            RankItem myselfRank = stage2.getRank().getMyselfRank();
            Long myselfRankId = null;
            if (myselfRank != null) {
                myselfRankId = myselfRank.getId();
            }

            for (int i = 0; i < rankList.size(); i++) {
                TeamVO teamVO = TeamVO.builder().id(rankList.get(i).getId()).name(rankList.get(i).getName()).icon(rankList.get(i).getIcon()).value(collect.getOrDefault(rankList.get(i).getId(), 0L)).build();
                if (i == 0 || i == 7 || i == 9) {
                    teamListList.get(0).add(teamVO);
                    if (teamVO.getId().equals(myselfRankId) && myselfRank != null) {
                        myselfRank.setExt("青龙");
                        myselfRank.setValue(teamVO.getValue());
                    }
                } else if (i == 1 || i == 6 || i == 8) {
                    teamListList.get(1).add(teamVO);
                    if (teamVO.getId().equals(myselfRankId) && myselfRank != null) {
                        myselfRank.setExt("白虎");
                        myselfRank.setValue(teamVO.getValue());
                    }
                } else if (i == 2 || i == 5 || i == 10) {
                    teamListList.get(2).add(teamVO);
                    if (teamVO.getId().equals(myselfRankId) && myselfRank != null) {
                        myselfRank.setExt("朱雀");
                        myselfRank.setValue(teamVO.getValue());
                    }
                } else if (i == 3 || i == 4 || i == 11) {
                    teamListList.get(3).add(teamVO);
                    if (teamVO.getId().equals(myselfRankId) && myselfRank != null) {
                        myselfRank.setExt("玄武");
                        myselfRank.setValue(teamVO.getValue());
                    }
                }
            }
            stage2.setTeam(teamListList);
            familyRankVO.setStage2(stage2);


        }
        if (indexVO.getStage() >= 3) {
            StageVO stage3 = new StageVO();
            stage3.setRank(eternalLoveRankManager.getRank(RankContext.builder()
                    .rankKey(String.format(FAMILY_RANK, 3))
                    .param(param)
                    .activityCode(ACTIVITY_CODE)
                    .type(RankContext.RankType.family)
                    .build()));
            List<List<TeamVO>> teamListList = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                teamListList.add(new ArrayList<>());
            }
            List<RankItem> rankList = familyRankVO.getStage2().getRank().getRankList();
            Map<Long, Long> collect = stage3.getRank().getRankList().stream().collect(Collectors.toMap(RankItem::getId, RankItem::getValue, (oldValue, newValue) -> oldValue));

            RankItem myselfRank = stage3.getRank().getMyselfRank();
            Long myselfRankId = null;
            if (myselfRank != null) {
                myselfRankId = myselfRank.getId();
            }

            for (int i = 0; i < rankList.size(); i++) {
                TeamVO teamVO = TeamVO.builder().id(rankList.get(i).getId()).name(rankList.get(i).getName()).icon(rankList.get(i).getIcon()).value(collect.getOrDefault(rankList.get(i).getId(), 0L)).build();
                if (i == 0 || i == 2 || i == 4 || i == 6) {
                    teamListList.get(0).add(teamVO);
                    if (teamVO.getId().equals(myselfRankId) && myselfRank != null) {
                        myselfRank.setExt("红队");
                        myselfRank.setValue(teamVO.getValue());
                    }
                } else if (i == 1 || i == 3 || i == 5 || i == 7) {
                    teamListList.get(1).add(teamVO);
                    if (teamVO.getId().equals(myselfRankId) && myselfRank != null) {
                        myselfRank.setExt("蓝队");
                        myselfRank.setValue(teamVO.getValue());
                    }
                }
            }
            stage3.setTeam(teamListList);
            familyRankVO.setStage3(stage3);
        }
        indexVO.setFamilyRank(familyRankVO);

        return indexVO;
    }

    public Boolean headFrameReceive(BaseParam param) {
        int globalFlowerGodValue = Optional.ofNullable(redisManager.getInteger(GLOBAL_FLOWER_GOD_VALUE)).orElse(0);
        if (globalFlowerGodValue < GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        if (!Boolean.TRUE.equals(redisManager.setnx(String.format(HEAD_FRAME_RECEIVE, param.getUid()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "global_flower_god_value");
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
        );
        notifyComponent.npcNotify(param.getUnionId(), param.getUid(), "恭喜亲爱的花神在活动中获得头像框奖励，奖励已经下发至装扮，快去查看吧。");

        return Boolean.TRUE;
    }

    public List<UserBasicVO> moreFriend(BaseParam param) {
        Set<Object> relationForm = Optional.ofNullable(redisManager.sGet(String.format(RELATIONSHIP_FORM, param.getUid()))).orElse(Collections.emptySet());
        List<UserVO> userVOS = feignUserService.acquireUsersBulkListPost(relationForm.stream().map(set -> Long.valueOf(String.valueOf(set))).collect(Collectors.toList())).successData();
        if (userVOS != null) {
            return userVOS.stream().map(userVO -> {
                UserBasicVO userBasicVO = new UserBasicVO();
                userBasicVO.setAvatar(userVO.getAvatar());
                userBasicVO.setId(userVO.getId());
                userBasicVO.setName(userVO.getName());
                return userBasicVO;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @NoRepeatSubmit(time = 3)
    public FlowerGodTokenExchangeResult flowerGodTokenExchange(BaseParam param, Integer num) {
        int flowerGodValue = Optional.ofNullable(redisManager.getInteger(String.format(FLOWER_GOD_VALUE, param.getUid()))).orElse(0);
        if (num == null || num == 0 || 3500 * num > flowerGodValue) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "您的花神值不够兑换噢~");
        }

        flowerGodValue = Math.toIntExact(Optional.ofNullable(redisManager.decrLong(String.format(FLOWER_GOD_VALUE, param.getUid()), 3500L * num, DateUtil.ONE_MONTH_SECOND)).orElse(0L));
        int flowerGodToken = Math.toIntExact(Optional.ofNullable(redisManager.incrLong(String.format(FLOWER_GOD_TOKEN, param.getUid()), num, DateUtil.ONE_MONTH_SECOND)).orElse(0L));

        return FlowerGodTokenExchangeResult.builder().flowerGodToken(flowerGodToken).myFlowerValue(flowerGodValue).build();
    }

    public FlowerGodVO confirmFriend(BaseParam param, Long toUid) {
        FlowerGodVO flowerGod = new FlowerGodVO();
        flowerGod.setUser(feignUserService.getBasic(param.getUid(), param.getAppId()).successData());
        flowerGod.setToUser(feignUserService.getBasic(toUid, param.getAppId()).successData());
        flowerGod.setGrowthValue(Math.min(29999, Optional.ofNullable(redisManager.getInteger(String.format(GROWTH_VALUE, AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0)));

        int curFlowerGod = Optional.ofNullable(redisManager.getInteger(String.format(CUR_FLOWER_GOD, AppUtil.splicUserId(param.getUid(), toUid)))).orElse(0);
        List<Boolean> flowerGodLight = Lists.newArrayListWithCapacity(6);
        for (int i = 0; i < 6; i++) {
            if (i < curFlowerGod) {
                flowerGodLight.add(Boolean.TRUE);
            } else {
                flowerGodLight.add(Boolean.FALSE);
            }
        }
        flowerGod.setFlowerGodLight(flowerGodLight);

        List<Integer> flowerGodButton = Lists.newArrayListWithCapacity(3);
        Set<Object> flowerGodDraw = redisManager.sGet(String.format(FLOWER_GOD_DRAW, AppUtil.splicUserId(param.getUid(), toUid)));
        if (Boolean.TRUE.equals(flowerGodLight.get(0)) && Boolean.TRUE.equals(flowerGodLight.get(1))) {
            if (flowerGodDraw.contains("FLOWER_GOD_1_2")) {
                flowerGodButton.add(1);
            } else {
                flowerGodButton.add(0);
            }
        } else {
            flowerGodButton.add(-1);
        }
        if (Boolean.TRUE.equals(flowerGodLight.get(2)) && Boolean.TRUE.equals(flowerGodLight.get(3))) {
            if (flowerGodDraw.contains("FLOWER_GOD_3_4")) {
                flowerGodButton.add(1);
            } else {
                flowerGodButton.add(0);
            }
        } else {
            flowerGodButton.add(-1);
        }
        if (Boolean.TRUE.equals(flowerGodLight.get(4)) && Boolean.TRUE.equals(flowerGodLight.get(5))) {
            if (flowerGodDraw.contains("FLOWER_GOD_5_6")) {
                flowerGodButton.add(1);
            } else {
                flowerGodButton.add(0);
            }
        } else {
            flowerGodButton.add(-1);
        }
        flowerGod.setFlowerGodButton(flowerGodButton);

        flowerGodLight.clear();
        for (int i = 0; i < 6; i++) {
            if (i <= curFlowerGod) {
                flowerGodLight.add(Boolean.TRUE);
            } else {
                flowerGodLight.add(Boolean.FALSE);
            }
        }

        List<Boolean> takePrize = Lists.newArrayListWithCapacity(3);
        Set<Object> flowerGodTakePrize = Optional.ofNullable(redisManager.sGet(String.format(FLOWER_GOD_TAKE_PRIZE, AppUtil.splicUserId(param.getUid(), toUid)))).orElse(Collections.emptySet());
        takePrize.add(flowerGodTakePrize.contains(999));
        takePrize.add(flowerGodTakePrize.contains(9999));
        takePrize.add(flowerGodTakePrize.contains(29999));
        flowerGod.setTakePrize(takePrize);

        return flowerGod;
    }

    @NoRepeatSubmit(time = 3)
    public FlowerGodVO flowerGodTakePrize(BaseParam param, Long toUid, Integer growthValue) {
        Set<Object> flowerGodTakePrize = Optional.ofNullable(redisManager.sGet(String.format(FLOWER_GOD_TAKE_PRIZE, AppUtil.splicUserId(param.getUid(), toUid)))).orElse(Collections.emptySet());
        if (flowerGodTakePrize.contains(growthValue)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        redisManager.sSetExpire(String.format(FLOWER_GOD_TAKE_PRIZE, AppUtil.splicUserId(param.getUid(), toUid)), DateUtil.ONE_MONTH_SECOND, growthValue);

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "flower_god_seed_" + growthValue);
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
        );
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(toUid).build(),
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, toUid)).collect(Collectors.toList())
        );

        if (flowerGodTakePrize.size() == 2) {
            Long curFlowerGod = Optional.ofNullable(redisManager.incrLong(String.format(CUR_FLOWER_GOD, AppUtil.splicUserId(param.getUid(), toUid)), 1L, DateUtil.ONE_MONTH_SECOND)).orElse(0L);
            if (curFlowerGod < 6) {
                redisManager.delete(String.format(FLOWER_GOD_TAKE_PRIZE, AppUtil.splicUserId(param.getUid(), toUid)));
                redisManager.delete(String.format(GROWTH_VALUE, AppUtil.splicUserId(param.getUid(), toUid)));
            }
        }

        for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
            eternalLoveTrackManager.allActivityReceiveAward(growthValue + "reward", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), param.getUid());
            eternalLoveTrackManager.allActivityReceiveAward(growthValue + "reward", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), toUid);
            notifyComponent.npcNotify(param.getUnionId(), param.getUid(), String.format("恭喜亲爱的花神，在”花神培育“中获得%s一个。礼物已经下发至您的背包，请注意查收噢。", scenePrizeDO.getPrizeDesc()));
            notifyComponent.npcNotify(param.getUnionId(), toUid, String.format("恭喜亲爱的花神，在”花神培育“中获得%s一个。礼物已经下发至您的背包，请注意查收噢。", scenePrizeDO.getPrizeDesc()));
        }



        return this.confirmFriend(param, toUid);
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public String getTemplateType() {
        return null;
    }

    public Long getCpByIntimacy(BaseParam param) {
        // from cache（30min valid）
        List<IntimacyUserInfoVO> coinResult = Lists.newArrayList();
        String cacheStr = (String) redisManager.get(String.format(ThanksgivingBattleConstant.INTIMACY_LIST, param.getUid()));
        if (Objects.nonNull(cacheStr)) {
            coinResult = JSONObject.parseObject(cacheStr, new TypeReference<List<IntimacyUserInfoVO>>() {
            });
        }

        // from coin
        if (CollectionUtils.isEmpty(coinResult)) {
            coinResult = feignSnsService.getOverValueIntimacyList(param.getUid(), 0, 1).successData();
            redisManager.set(String.format(ThanksgivingBattleConstant.INTIMACY_LIST, param.getUid()), JSONObject.toJSONString(coinResult), RedisManager.ONE_MINUTE_SECONDS * 30);
            if (org.springframework.util.CollectionUtils.isEmpty(coinResult)) {
                return null;
            }
        }
        return coinResult.get(0).getUser().getId();
    }

    public DrawLogVO drawLog(BaseParam param) {
        List<DrawLogDO> drawLogDOList = drawLogJpaDAO.findAll(Example.of(DrawLogDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .toUid(param.getUid())
                .activityCode(ACTIVITY_CODE)
                .build()));

        List<DrawLogItem> drawLogWrapperList = eternalLoveDrawManager.drawLogWrapper(DrawLogParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(), drawLogDOList);

        DrawLogVO drawLogVO = new DrawLogVO();
        Map<String, List<DrawLogItemVO>> timeDrawLogItemMap = new HashMap<>();
        for (DrawLogItem drawLogItem : drawLogWrapperList) {
            if (timeDrawLogItemMap.containsKey(drawLogItem.getTime())) {
                timeDrawLogItemMap.get(drawLogItem.getTime()).add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
            } else {
                List<DrawLogItemVO> drawLogItemList = new ArrayList<>();
                drawLogItemList.add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
                timeDrawLogItemMap.put(drawLogItem.getTime(), drawLogItemList);
            }
        }
        List<DrawLogTimeItemVO> drawLogTimeItemList = new ArrayList<>();
        timeDrawLogItemMap.forEach((k, v) -> drawLogTimeItemList.add(DrawLogTimeItemVO.builder().time(k).drawLogItemList(v).build()));
        drawLogTimeItemList.sort((o1, o2) -> o2.getTime().compareTo(o1.getTime()));
        drawLogVO.setDrawLogTimeItemList(drawLogTimeItemList);

        return drawLogVO;
    }

}
