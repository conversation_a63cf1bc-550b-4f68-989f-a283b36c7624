package cn.yizhoucp.ump.biz.project.dal.mybatis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TableName("activity_message_templates")
public class ActivityMessageTemplates {

    public static final String ACTIVITY_CODE = "activity_code";
    public static final String SCENE_CODE = "scene_code";
    @TableId
    private String id;

    private String activityCode;

    private String sceneCode;

    private String messageDesc;

    private String messageTemplate;

    private String configVersion;

    private LocalDateTime createAt;

    private LocalDateTime updateAt;


}
