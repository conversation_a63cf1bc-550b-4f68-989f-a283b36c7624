package cn.yizhoucp.ump.biz.common.core.biz;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 0.1 : ComponentAutowireFactory v0.1, created at 07/12/2016 11:07 AM, created by z<PERSON>qin Exp $
 */
@Component
public class ComponentAutowireFactory implements ApplicationContextAware {

    private static ApplicationContext _applicationContext;

    public static void autowire(Object bean) {
        _applicationContext.getAutowireCapableBeanFactory().autowireBean(bean);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        _applicationContext = applicationContext;
    }
}
