package cn.yizhoucp.ump.biz.project.biz.manager.activity.callBack;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.product.dto.UserPackageDetailDTO;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.ump.api.enums.ActivityTemplateEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace.AppAdSpaceManager;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.LuckyBagManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.UserActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountRemoteService;
import cn.yizhoucp.ump.biz.project.common.ActivityTemplate;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BuoyInfoHandler;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.*;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.*;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.callback.LuckyBagCallBackVO;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;

/**
 * 福袋大户唤回活动
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LuckyBagCallBigRichBackManager implements ActivityTemplate, BuoyInfoHandler {

    @Resource
    @Lazy
    private LuckyBagManager luckyBagManager;
    @Resource
    private LuckyBagStyleJpaDAO luckyBagStyleJpaDAO;
    @Resource
    private UserActivityManager userActivityManager;
    @Resource
    private AppAdSpaceManager appAdSpaceManager;
    @Resource
    private CycleActivityJpaDAO cycleActivityJpaDAO;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;
    @Resource
    private UserActivityJpaDAO userActivityJpaDAO;
    @Resource
    private UserAccountRemoteService userAccountRemoteService;

    /** 埋点事件名 */
    private static final String INVITE_ACTIVITY_USE = "recall_lucky_activity_use";

    public static final String FREE_TIMES = "free_times";
    public static final String COUPON = "coupon";

    /**
     * 获取唤回首页
     *
     * @param param 基础信息
     * @return LuckyBagCallBackVO
     */
    public LuckyBagCallBackVO getIndexInfo(BaseParam param) {
        LuckyBagCallBackVO result = LuckyBagCallBackVO.builder().build();
        if (Objects.isNull(param.getAppId()) || StringUtils.isBlank(param.getUnionId())) {
            return result;
        }

        // 查询用户参与活动
        UserActivityDO activity = userActivityJpaDAO.getUserJoinActivityByTemplate(param.getAppId(), param.getUnionId(), param.getUid(), getTemplateType());
        if (Objects.isNull(activity)) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "系统繁忙，请稍后再试");
        }
        // 填充配置样式
        result = fillStyle(param, activity.getBelongTemplateCode(), result);
        // 填充奖品配置
        result = fillPrizeInfo(param, activity.getActivityCode(), result);
        return result;
    }

    /**
     * 获取浮标信息
     *
     * @param
     * @return
     */
    @Override
    public Function<HandlerContext, BuoyInfoDTO> getBuoyInfoHandler() {
        return (context) -> {
            // 获取用户参与活动配置
            List<PrizeItem> configPrizes = getJoinedActivityConfigPrizes(context.getAppId(), context.getUnionId(), context.getUid());
            if (CollectionUtils.isEmpty(configPrizes)) {
                return BuoyInfoDTO.builder().build();
            }

            // 查询剩余体验券
            UserPackageDetailDTO info = getPackageByBizId(context.getAppId(), context.getUid(), configPrizes.get(0).getPrizeKey());
            if (Objects.nonNull(info) && Objects.nonNull(info.getAvailableNum()) && info.getAvailableNum() < 1) {
                return BuoyInfoDTO.builder().build();
            }
            log.info("luckyBagCallBack 获取券信息 info:{}", JSON.toJSONString(info));

            // 拼装角标、倒计时信息（仅展示 24 小时内）
            return BuoyInfoDTO.builder()
                    .showNum(info.getAvailableNum())
                    .countDownTime(getCountDownTime(info.getTimeLimit())).build();
        };
    }

    /**
     * 更新浮标信息
     *
     * @param param 基础参数
     * @return Boolean
     */
    public Boolean updateBuoyNum(BaseParam param) {
        // 获取用户参与活动配置
        List<PrizeItem> configPrizes = getJoinedActivityConfigPrizes(param.getAppId(), param.getUnionId(), param.getUid());
        if (CollectionUtils.isEmpty(configPrizes)) {
            return Boolean.FALSE;
        }

        // 查询剩余体验券数量
        UserPackageDetailDTO info = getPackageByBizId(param.getAppId(), param.getUid(), configPrizes.get(0).getPrizeKey());

        // 退出活动判定
        if (info.getAvailableNum() < 1) {
            userActivityManager.exitUserActivity(param.getAppId(), param.getUnionId(), param.getUid(), getTemplateType());
        }

        // 通知客户端更新
        return appAdSpaceManager.refreshUserAdSpace(param);
    }

    /**
     * 埋点
     *
     * @param appId 应用id
     * @param uid   用户id
     * @param type  类型
     */
    public void luckyBagCallbackTrack(Long appId, Long uid, String type) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        yzKafkaProducerManager.dataRangerTrack(appId, uid, INVITE_ACTIVITY_USE, params, ServicesNameEnum.ump_services.getCode());
    }

    @Override
    public String getTemplateType() {
        return ActivityTemplateEnum.LUCKY_BAG_CALL_BACK_DAILY.getCode();
    }

    @Override
    public String getActivityCode() {
        return null;
    }


    private LuckyBagCallBackVO fillPrizeInfo(BaseParam param, String activityCode, LuckyBagCallBackVO result) {
        List<CycleActivityDO> cycleActivityDOList = cycleActivityJpaDAO.findAll(Example.of(CycleActivityDO.builder().activityCode(activityCode).status(1).build()));
        if (CollectionUtils.isEmpty(cycleActivityDOList)) {
            return result;
        }
        try {
            result.setPrizeItems(JSON.parseObject(cycleActivityDOList.get(0).getConfig()).getJSONObject("handlerContext").getJSONObject("joinActivityChain").getJSONArray("sendPrizeConfig").toJavaList(PrizeItem.class));
        } catch (Exception e) {
            log.error("数据结构转换失败 uid:{}, config:{}", param.getUid(), cycleActivityDOList.get(0).getConfig());
        }
        return result;
    }

    private LuckyBagCallBackVO fillStyle(BaseParam param, String templateCode, LuckyBagCallBackVO result) {
        // 获取配置信息
        List<LuckyBagStyleDO> styles = luckyBagStyleJpaDAO.findAll(Example.of(LuckyBagStyleDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .activityCode(templateCode).build()));
        if (CollectionUtils.isEmpty(styles)) {
            return result;
        }
        JSONObject obj = new JSONObject();
        try {
            styles.forEach(s -> {
                if (Objects.equals("prizeItems", s.getStyleKey())) {
                    obj.put("prizeItems", JSONObject.parseArray(s.getStyleValue(), PrizeItem.class));
                    return;
                }
                obj.put(s.getStyleKey(), s.getStyleValue());
            });
            return obj.toJavaObject(LuckyBagCallBackVO.class);
        } catch (Exception e) {
            log.error("数据结构转换失败 uid:{}, obj:{}", param.getUid(), obj);
        }
        return result;
    }

    private Long getCountDownTime(Long timeLimit) {
        if (LocalDateTime.now().plusDays(1).isAfter(Instant.ofEpochMilli(timeLimit).atZone(ZoneId.systemDefault()).toLocalDateTime())) {
            return timeLimit;
        }
        return null;
    }

    private List<PrizeItem> getJoinedActivityConfigPrizes(Long appId, String unionId, Long uid) {
        List<PrizeItem> result = Lists.newArrayList();
        UserActivityDO activity = userActivityJpaDAO.getUserJoinActivityByTemplate(appId, unionId, uid, getTemplateType());
        if (Objects.isNull(activity)) {
            return result;
        }
        List<CycleActivityDO> cycleActivityDOList = cycleActivityJpaDAO.findAll(Example.of(CycleActivityDO.builder().activityCode(activity.getActivityCode()).status(1).build()));
        if (CollectionUtils.isEmpty(cycleActivityDOList)) {
            return result;
        }
        List<PrizeItem> configPrizes = JSON.parseObject(cycleActivityDOList.get(0).getConfig()).getJSONObject("handlerContext").getJSONObject("joinActivityChain").getJSONArray("sendPrizeConfig").toJavaList(PrizeItem.class);
        if (CollectionUtils.isEmpty(configPrizes)) {
            return result;
        }
        return configPrizes;
    }

    private UserPackageDetailDTO getPackageByBizId(Long appId, Long uid, String bizId) {
        UserPackageDetailDTO result = new UserPackageDetailDTO();
        List<UserPackageDetailDTO> packageList = userAccountRemoteService.getPackageByBizIdListAndType(appId, uid, getCouponBizIdList(), UserPackageBizType.COIN_COUPON.getCode());
        if (CollectionUtils.isEmpty(packageList)) {
            result.setAvailableNum(0L);
            return result;
        }
        UserPackageDetailDTO userPackage = packageList.get(0);
        if (packageList.size() > 1) {
            for (int i = 1; i < packageList.size(); i++) {
                if (!StringUtils.equals(bizId, packageList.get(i).getBizId())) {
                    continue;
                }
                // 取总计数量
                userPackage.setTotalNum(userPackage.getTotalNum() + packageList.get(i).getTotalNum());
                userPackage.setAvailableNum(userPackage.getAvailableNum() + packageList.get(i).getAvailableNum());
                // 取最久过期时间
                if (Optional.ofNullable(packageList.get(i).getTimeLimit()).orElse(0L) > Optional.ofNullable(userPackage.getTimeLimit()).orElse(0L)) {
                    userPackage.setTimeLimit(packageList.get(i).getTimeLimit());
                }
            }
        }
        log.info("userPackage {}", JSON.toJSONString(userPackage));
        return userPackage;
    }

    private List<String> getCouponBizIdList() {
        List<String> result = Lists.newArrayList();
        for (CouponEnum item : CouponEnum.values()) {
            result.add(item.getCode());
        }
        return result;
    }

}
