package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.common.LoveInBloomFestivalEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;

@Component
public class LoveInBloomFestivalStrategyChoose extends StrategyManager<LoveInBloomFestivalEnums.ButtonEnum, ExecutableStrategy> {
}
