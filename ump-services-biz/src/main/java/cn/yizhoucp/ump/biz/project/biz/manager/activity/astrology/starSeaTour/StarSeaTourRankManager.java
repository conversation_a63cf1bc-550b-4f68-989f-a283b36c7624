package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaTour;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.TOUR_BOARD;

@Service
@Slf4j
public class StarSeaTourRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    @Lazy
    private StarSeaTourBizManager starSeaTourBizManager;

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(10L);
    }

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    public Boolean starSeaTourSendPrize(String rankKey) {
        if (StarSeaTourConstant.GUIDE_BOARD.equals(rankKey)) {
            if (!Boolean.TRUE.equals(redisManager.setnx("ump:star_sea_tour2:guide_board:send_prize_idempotent", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "guide_board");
            log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
            if (CollectionUtils.isEmpty(scenePrizeDOList)) {
                return Boolean.FALSE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(rankKey)
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );

                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        rankItem.getId(),
                        String.format("恭喜在「星海巡游记」活动中，守护榜排第%s名，获取%s礼物一个，奖励已经下发背包，快去查看吧～", rankItem.getRank(),scenePrizeDOs.get(0).getPrizeDesc())
                );

            }
        } else if (StarSeaTourConstant.TOUR_BOARD_OVERALL.equals(rankKey)) {
            if (!Boolean.TRUE.equals(redisManager.setnx("ump:star_sea_tour2:tour_board_overall:send_prize_idempotent", System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "tour_board_overall");
            log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
            if (CollectionUtils.isEmpty(scenePrizeDOList)) {
                return Boolean.FALSE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(rankKey)
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scenePrizeDOs) || rankItem.getValue() < 10000) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );

                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        rankItem.getId(),
                        String.format("恭喜在「星海巡游记」活动中，巡游总榜排第%s名，获取%s礼物一个，奖励已经下发背包，快去查看吧～", rankItem.getRank(),scenePrizeDOs.get(0).getPrizeDesc())
                );

            }
        } else {
            if (!Boolean.TRUE.equals(redisManager.setnx(String.format("ump:star_sea_tour2:tour_board:send_prize_idempotent:%s", starSeaTourBizManager.getDate()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                return Boolean.TRUE;
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "tour_board");
            log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
            if (CollectionUtils.isEmpty(scenePrizeDOList)) {
                return Boolean.FALSE;
            }

            RankVO rankVO = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(String.format(TOUR_BOARD, ActivityTimeUtil.getYesterdayStr(null)))
                    .type(RankContext.RankType.user)
                    .build());
            log.info("rankVO {}", rankVO);
            if (rankVO == null) {
                return Boolean.FALSE;
            }

            List<RankItem> rankList = rankVO.getRankList();
            if (CollectionUtils.isEmpty(rankList)) {
                return Boolean.FALSE;
            }

            for (RankItem rankItem : rankList) {
                Long rank = rankItem.getRank();
                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scenePrizeDOs) || rankItem.getValue() < 3000) {
                    log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                    continue;
                }
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        rankItem.getId(),
                        String.format("恭喜在「星海巡游记」活动中，昨日排第%s名，获取%s礼物一个，奖励已经下发背包，快去查看吧～", rankItem.getRank(),scenePrizeDOs.get(0).getPrizeDesc())
                );

            }
        }

        return Boolean.TRUE;
    }

}
