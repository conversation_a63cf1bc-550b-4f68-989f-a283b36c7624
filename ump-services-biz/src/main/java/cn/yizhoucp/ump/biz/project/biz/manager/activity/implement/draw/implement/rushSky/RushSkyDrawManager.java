package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.implement.rushSky;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.international.InternationalManager;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList.DefaultBarrageManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.RushSkyCommonManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.RushSkyReportManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class RushSkyDrawManager extends AbstractDrawTemplate {

    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DrawPoolService drawPoolService;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private RushSkyCommonManager rushSkyCommonManager;
    @Resource
    private DefaultBarrageManager defaultBarrageManager;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private RushSkyReportManager rushSkyReportManager;
    @Resource
    private InternationalManager internationalManager;

    /** 奖池名称 */
    private String DRAW_POOL = "rushSkyDrawPool";

    @Override
    protected void resourceCheck(DrawContext context) {
        // 单一场景暂不考虑并发问题
        DrawParam drawParam = context.getDrawParam();
        if (Objects.isNull(drawParam) || drawParam.getTimes() < 1) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "抽奖次数错误");
        }
        Long userCouponNum = rushSkyCommonManager.getUserCouponNum(drawParam.getUid());
        if (userCouponNum < 10 * drawParam.getTimes()) {
            throw new ServiceException(ErrorCode.MISS_PARAM, internationalManager.format("simple.activity.saka.thesky.resource.check"));
        }
        rushSkyCommonManager.useCoupon(drawParam.getUid(), 10L * drawParam.getTimes());
    }

    @Override
    protected void draw(DrawContext context) {
        // 获取奖池配置信息
        DrawPoolDO drawPoolDO = drawPoolService.getByActivityCodeAndPoolCodeAndEnable(ActivityCheckListEnum.RUSH_SKY.getCode(), DRAW_POOL);
        context.setDrawPoolDO(drawPoolDO);
        // 获取奖品列表
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(drawPoolDO.getPoolCode());
        context.setDrawPoolItemDOS(drawPoolItemDOList);
        // 获取抽奖结果
        List<DrawPoolItemDTO> prizeItems = probStrategy.getDrawPoolItems(context);
        context.setPrizeItemList(prizeItems);
    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {
        UserVO user = userRemoteService.getBasic(context.getDrawParam().getUid(), context.getDrawParam().getAppId(), Boolean.TRUE);
        // 拼接获奖信息
        List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
        if (CollectionUtils.isEmpty(prizeItemList)) {
            return;
        }
        StringBuilder desc = new StringBuilder("");
        prizeItemList.stream().forEach(p -> desc.append(internationalManager.format(p.getDrawPoolItemDO().getItemName())).append("、"));
        // 添加弹幕
        defaultBarrageManager.putBarrage(internationalManager.format("simple.activity.saka.thesky.draw.barrage", user.getName(), StringUtils.removeEnd(desc.toString(), "、")));
        // 埋点
        prizeItemList.stream().forEach(p -> {
            rushSkyReportManager.drawReport(user.getId(), context.getDrawParam().getTimes(), p.getDrawPoolItemDO().getItemKey());
        });
    }
}
