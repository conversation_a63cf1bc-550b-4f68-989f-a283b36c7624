package cn.yizhoucp.ump.biz.project.biz.enums.activity.rushSky;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 家族 PK 等级信息
 *
 * @author: lianghu
 */
@Getter
@AllArgsConstructor
public enum FamilyPkLevelInfo {

    LEVEL_1(1, "4w", "0.8w", "2000 + %s", "", 40 * 1000L, 8 * 1000L),
    LEVEL_2(2, "2w", "0.75w", "1000 + %s", "", 20 * 1000L, 7500L),
    LEVEL_3(3, "1w", "0.7w", "500 + %s", "", 10 * 1000L, 7 * 1000L),
    LEVEL_4(4, "0.9w", "0.6w", "450 + %s", "", 9000L, 6 * 1000L),
    LEVEL_5(5, "0.85w", "0.5w", "425 + %s", "", 8500L, 5 * 1000L),
    LEVEL_6(6, "0.8w", "", "400 + %s", "", 8000L, 0L),
    LEVEL_7(7, "0.75w", "", "375 + %s", "", 7500L, 0L),
    LEVEL_8(8, "0.7w", "", "350 + %s", "", 7000L, 0L),
    LEVEL_9(9, "0.6w", "", "300 + %s", "", 6000L, 0L),
    LEVEL_10(10, "0.5w", "", "250 + %s", "", 5000L, 0L),
    LEVEL_11(11, "0.35w", "", "175 + %s", "", 3500L, 0L),
    LEVEL_12(12, "0.35w", "", "175 + %s", "", 3500L, 0L),
    LEVEL_13(13, "0.35w", "", "175 + %s", "", 3500L, 0L),
    LEVEL_14(14, "0.35w", "", "175 + %s", "", 3500L, 0L),
    LEVEL_15(15, "0.35w", "", "175 + %s", "", 3500L, 0L),
    ;
    private Integer level;
    private String minimumMedalNumDesc;
    private String chatieMinimumMedalNumDesc;
    private String bonusDesc;
    private String chatieBonusDesc;
    private Long minimumMedalNum;
    private Long chatieMinimumMedalNum;

    private static final Map<Integer, FamilyPkLevelInfo> instanceMap = new ConcurrentHashMap<>();

    static {
        for (FamilyPkLevelInfo item : values()) {
            instanceMap.put(item.getLevel(), item);
        }
    }


    public static FamilyPkLevelInfo get(Integer level) {
        if (Objects.isNull(level)) {
            return null;
        }
        return instanceMap.get(level);
    }
}
