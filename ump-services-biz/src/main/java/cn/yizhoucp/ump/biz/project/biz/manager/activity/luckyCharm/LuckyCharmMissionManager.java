package cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyCharm;

import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.enums.EnvType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.param.jimu.QueryMissionParam;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyCharm.NotifyEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyCharm.TaskCodeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ActivityReportManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractMissionTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.MissionContext;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityMissionJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.UserMissionJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserMissionDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyCharmConstant.*;


/**
 * 春节锦鲤任务
 * <p>
 * 3. 任务完成处理
 * 4. 任务 MQ 接入
 * 5. 任务 check
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class LuckyCharmMissionManager extends AbstractMissionTemplate {

    @Resource
    private ActivityMissionJpaDAO missionJpaDAO;
    @Resource
    private LuckyCharmBizManager luckyCharmBizManager;
    @Resource
    private ActivityStatusManager activityStatusManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private UserMissionJpaDAO userMissionJpaDAO;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private ActivityReportManager activityReportManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private PopManager popManager;
    @Resource
    private NotifyComponent notifyComponent;

    @Override
    public TaskVO getMissionList(QueryMissionParam param) {
        // 初始化
        init(MissionContext.ofBaseParam(param));

        // 查询任务列表
        List<UserMissionDO> missionList = userMissionJpaDAO.findAll(Example.of(UserMissionDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).userId(param.getUid()).belongActivityCode(luckyCharmBizManager.getActivityCode()).status("default").build()));

        // 同类型去重 & 排序
        missionList = distinctByKey(param.getBaseParam(), missionList);

        // 补充元素
        List<TaskItem> result = fillProperty(param, missionList);
        return TaskVO.builder().taskItemList(result).build();
    }

    private List<UserMissionDO> distinctByKey(BaseParam param, List<UserMissionDO> missionList) {
        if (CollectionUtils.isEmpty(missionList)) {
            return missionList;
        }
        // 分组
        List<UserMissionDO> result = Lists.newArrayList();
        Map<String, List<UserMissionDO>> groupMap = missionList.stream().collect(Collectors.groupingBy(UserMissionDO::getGroupId));
        for (Map.Entry<String, List<UserMissionDO>> item : groupMap.entrySet()) {
            if ("default".equals(item.getKey())) {
                result.addAll(item.getValue());
            } else {
                int size = item.getValue().size();
                int index = 0;
                for (UserMissionDO m : item.getValue()) {
                    if (++index == size) {
                        result.add(m);
                        break;
                    }
                    // 未完成添加
                    if (getCompletedTimes(param, m.getCode()) < m.getLimitTimes()) {
                        result.add(m);
                        break;
                    }
                }
            }
        }
        return result.stream().sorted(Comparator.comparing(UserMissionDO::getPriority).reversed()).collect(Collectors.toList());
    }

    private List<TaskItem> fillProperty(BaseParam param, List<UserMissionDO> missionList) {
        List<TaskItem> result = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(missionList)) {
            result = missionList.stream().map(m -> {
                TaskItem taskItem = m.convert2TaskItem();
                // 设置按钮状态
                taskItem.setTaskStatus(getCompletedTimes(param, m.getCode()) >= m.getLimitTimes() ? 1 : 0);
                // 路由补充
                if ("draw".equals(m.getGroupId())) {
                    taskItem.setTaskRoute(getChatRoomId());
                } else if ("sendGiftFamily".equals(m.getCode())) {
                    FamilyInfoDTO family = familyFeignService.findFamilyInfoByUid(param.getUid(), param.getAppId()).successData();
                    log.debug("luckyCharm 查询用户家族 uid:{}, appId:{}, family:{}", param.getUid(), param.getAppId(), JSON.toJSONString(family));
                    if (Objects.nonNull(family) && Objects.nonNull(family.getId())) {
                        taskItem.setTaskRoute(String.format("https://router.nuan.chat/conversation-family?family_id=%s&chat_id=%s", family.getId(), family.getChatId()));
                    }
                }
                return taskItem;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    protected Boolean doCheck(MissionContext context) {
        log.debug("luckyCharm 业务验证 context:{}", JSON.toJSONString(context));
        MissionParam missionParam = context.getMissionParam();
        if (!activityStatusManager.activityIsEnable(missionParam.getBaseParam(), getActivityCode())) {
            return Boolean.FALSE;
        }
        // 验证是否完成
        if (!completedCheck(context)) {
            log.debug("luckyCharm 任务已完成 uid:{}, taskCode:{}", missionParam.getUid(), missionParam.getTaskCode());
            return Boolean.FALSE;
        }
        Integer times = getLuckyDrawTimes(missionParam.getBaseParam());

        // 抽取福袋次数验证
        if (TaskCodeEnum.luckyBag1.name().equals(missionParam.getTaskCode())) {
            if (times >= 1) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        }
        if (TaskCodeEnum.luckyBag2.name().equals(missionParam.getTaskCode())) {
            if (times >= 100) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        }
        if (TaskCodeEnum.luckyBag3.name().equals(missionParam.getTaskCode())) {
            if (times >= 521) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        }
        // 送礼、视频聊天走 CheckerChain，文字聊天、邀请注册直接通过
        return Boolean.TRUE;
    }

    @Override
    protected Boolean init(MissionContext context) {
        BaseParam param = context.getMissionParam().getBaseParam();
        if (!activityStatusManager.activityIsEnable(param, luckyCharmBizManager.getActivityCode())) {
            log.debug("luckyCharm 活动未开启");
            return Boolean.FALSE;
        }

        // 初始化幂等保证
        if (!redisManager.setnx(String.format(INIT_MISSION, param.getUid(), ActivityTimeUtil.getToday(luckyCharmBizManager.getActivityCode())), System.currentTimeMillis() + "", RedisManager.ONE_DAY_SECONDS * 3)) {
            log.debug("luckyCharm 重复初始化");
            return Boolean.FALSE;
        }

        // 过期昨日任务
        List<UserMissionDO> expiredMissions = userMissionJpaDAO.findAll(Example.of(UserMissionDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .userId(param.getUid())
                .belongActivityCode(luckyCharmBizManager.getActivityCode())
                .build()));
        if (!CollectionUtils.isEmpty(expiredMissions)) {
            expiredMissions = expiredMissions.stream().filter(m -> m.getExpiredTime().isBefore(ActivityTimeUtil.getNow(luckyCharmBizManager.getActivityCode()))).map(m -> {
                m.setStatus("expired");
                return m;
            }).collect(Collectors.toList());
            userMissionJpaDAO.saveAll(expiredMissions);
            log.info("luckyCharm 任务过期处理 uid:{}", param.getUid());
        }

        // 查询任务配置
        List<ActivityMissionDO> missions = missionJpaDAO.findAll(Example.of(ActivityMissionDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).belongActivityCode(luckyCharmBizManager.getActivityCode()).status("enable").build()));
        if (CollectionUtils.isEmpty(missions)) {
            log.debug("luckyCharm 任务配置为空");
            return Boolean.FALSE;
        }

        // 初始化
        List<UserMissionDO> result = Lists.transform(missions, p -> {
            UserMissionDO m = UserMissionDO.of(param, p);
            m.setExpiredTime(ActivityTimeUtil.getLastTimeOfToday(luckyCharmBizManager.getActivityCode()));
            return m;
        });
        if (!CollectionUtils.isEmpty(result)) {
            userMissionJpaDAO.saveAll(result);
            log.info("luckyCharm 任务初始化成功 uid:{}, result:{}", param.getUid(), JSON.toJSON(result));
        }

        return Boolean.TRUE;
    }

    @Override
    protected Boolean completedProcess(MissionContext context) {
        log.info("luckyCharm 任务完成 param:{}", JSON.toJSONString(context));
        MissionParam missionParam = context.getMissionParam();
        BaseParam param = missionParam.getBaseParam();
        TaskCodeEnum taskEnum = TaskCodeEnum.get(missionParam.getTaskCode());
        String giftKey = getGiftKey(missionParam.getBizParam());
        Long prizeNum = taskEnum.getPrizeNum(giftKey);

        // 记录完成次数
        incrCompletedTimes(param, taskEnum.name());

        // 下发锦鲤奖励
        sendPrizeManager.sendGift(param.getAppId(), taskEnum.getPrizeKey(), param.getUid(), prizeNum, 15, luckyCharmBizManager.getActivityCode());

        // 弹窗
        popManager.popByCode(param.getAppId(), param.getUnionId(), param.getUid(), taskEnum.getBizPopCode(giftKey));

        // 任务埋点
        activityReportManager.missionCompletedReport(param, getActivityCode(), taskEnum.name(), giftKey + prizeNum);

        // 小助手通知
        String text = "";
        switch (taskEnum.getPrizeKey()) {
            case "JL_GIFT":
                text = NotifyEnum.COMPLETED_MISSION_CHARM.getText(prizeNum.toString(), luckyCharmBizManager.getActivityUrl(param));
                break;
            case "JJL_GIFT":
                text = NotifyEnum.COMPLETED_MISSION_GOLD_CHARM.getText(prizeNum.toString(), luckyCharmBizManager.getActivityUrl(param));
                break;
        }
        if (StringUtils.isNotBlank(text)) {
            notifyComponent.npcNotify(param.getUid(), text);
        }
        return Boolean.TRUE;
    }

    private Integer incrCompletedTimes(BaseParam param, String taskCode) {
        log.debug("luckyCharm incrCompletedTimes key:{}", String.format(USER_MISSION_LOG, param.getUid(), ActivityTimeUtil.getToday(luckyCharmBizManager.getActivityCode())));
        Double after = redisManager.hincr(String.format(USER_MISSION_LOG, param.getUid(), ActivityTimeUtil.getToday(luckyCharmBizManager.getActivityCode())), taskCode, 1, DateUtil.ONE_MONTH_SECOND);
        return after.intValue();
    }

    private Integer getCompletedTimes(BaseParam param, String taskCode) {
        return Optional.ofNullable((Integer) redisManager.hget(String.format(USER_MISSION_LOG, param.getUid(), ActivityTimeUtil.getToday(luckyCharmBizManager.getActivityCode())), taskCode)).orElse(0);
    }

    private Integer getHashIndex(Object hashItem) {
        return hashItem.hashCode() % 10;
    }

    private String getChatRoomId() {
        EnvType envType = EnvType.convertFormEnv(env);
        if (Objects.isNull(envType)) {
            envType = EnvType.DEV;
        }
        switch (envType) {
            case LOCAL:
            case DEV:
                return "61846";
            case TEST:
                return "61093";
            case PRE:
            case PROD:
                return "11457107";
            default:
                log.error("未接入环境 env:{}", env);
                return "";
        }
    }

    @Override
    public String getActivityCode() {
        return luckyCharmBizManager.getActivityCode();
    }


    private Integer getLuckyDrawTimes(BaseParam param) {
        return Optional.ofNullable((Integer) redisManager.hget(String.format(LUCKY_DRAW_TIMES, ActivityTimeUtil.getToday(luckyCharmBizManager.getActivityCode())), param.getUid().toString())).orElse(0);
    }

    private String getGiftKey(JSONObject param) {
        JSONArray gifts = param.getJSONArray("coinGiftGivedModels");
        if (!CollectionUtils.isEmpty(gifts)) {
            return gifts.toJavaList(CoinGiftGivedModel.class).get(0).getGiftKey();
        }
        return "";
    }

    private Boolean completedCheck(MissionContext context) {
        MissionParam missionParam = context.getMissionParam();
//        UserMissionDO record = missionParam.getBizParam().getObject("record", UserMissionDO.class);
//        log.debug("completedTimes:{}, limit:{}", getCompletedTimes(missionParam.getBaseParam(), missionParam.getTaskCode()), record.getLimitTimes());
//        if (getCompletedTimes(missionParam.getBaseParam(), missionParam.getTaskCode()) >= record.getLimitTimes()) {
//            return Boolean.FALSE;
//        }
        return Boolean.TRUE;
    }

}
