package cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.UsePackageDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.PalpitatingHeartYouConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou.internal.PalpitaingHeartYouEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou.internal.PalpitatingHeartYouRedisMangaer;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.ActivityMessageTemplates;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.ActivityMessageTemplatesService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PalpitatingHeartYouConstant.*;

@Service
@Slf4j
public class PalpitatingHeartYouRankManager extends AbstractRankManager {

    @Resource
    private RedisManager redisManager;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private PalpitatingHeartYouTrackManager trackManager;

    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private PalpitatingHeartYouBizManager palpitatingHeartYouBizManager;
    @Resource
    private ActivityMessageTemplatesService activityMessageTemplatesService;
    @Resource
    private PalpitatingHeartYouRedisMangaer palpitatingHeartYouRedisMangaer;
    @Autowired
    private PalpitatingHeartYouTrackManager palpitatingHeartYouTrackManager;

    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(10L);
    }


    /**
     * 兑换
     */
    public Boolean exchange(String activityCode, String rewardsKey) {
        // 校验活动
        if (StringUtils.isBlank(activityCode) || StringUtils.isBlank(rewardsKey) || !activityCode.equals(ACTIVITY_CODE)) {
            log.warn("getTheaterLeaderboardByDate error activityCode:{}", activityCode);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = MDCUtil.getCurUserIdByMdc();
        if (Objects.isNull(uid)) {
            log.warn("MDCUtil getCurUserIdByMdc is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        PalpitaingHeartYouEnum.RewardEnum rewardEnum = PalpitaingHeartYouEnum.RewardEnum.getByRewardsKey(rewardsKey);
        if (Objects.isNull(rewardEnum)) {
            log.warn("rewardsKey getByGiftKey is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        // 校验用户心动卡片数量
        String key = String.format(palpitatingHeartYouBizManager.buildActivityTimeKey(HEART_BEAT_KEY), uid);
        Long heartNumber = Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
        if (heartNumber <= 0 || (heartNumber - rewardEnum.getRewardCount()) < 0) {
            log.warn("the user s heartbeat card exchange is abnormal uid:{}", uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "爱神卡片数量不足");
        }
        // 兑换扣减
        redisManager.subtractLong(key, -rewardEnum.getRewardCount(), DateUtil.ONE_MONTH_SECOND);

        // 兑换发放
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), ACTIVITY_CODE, rewardEnum.getRewardCode());
        log.info("exchangeRelease scenePrizeDOList :{} -> uid:{}, code:{}", scenePrizeDOList, uid, rewardEnum.getRewardCode());
        sendPrizeManager.sendPrize(
                BaseParam.builder()
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .uid(uid)
                        .build()
                , scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
        for(ScenePrizeDO scenePrizeDO : scenePrizeDOList){
            palpitatingHeartYouTrackManager.allActivityReceiveAward(uid, scenePrizeDO.getPrizeValue(),"love_letter_exchange_station", scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum());
        }
        return Boolean.TRUE;
    }

    /**
     * 合成
     */
    public Long craft(String activityCode) {
        // 校验
        if (StringUtils.isBlank(activityCode) || !activityCode.equals(ACTIVITY_CODE)) {
            log.warn("getTheaterLeaderboardByDate error activityCode:{}", activityCode);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = MDCUtil.getCurUserIdByMdc();
        Long appId = MDCUtil.getCurAppIdByMdc();
        String unionId = MDCUtil.getCurUnionIdByMdc();
        if (Objects.isNull(uid) || Objects.isNull(appId) || StringUtils.isBlank(unionId)) {
            log.warn("MDCUtil getCurUserIdByMdc is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        // 获取最少的碎片卡数量
        String key = String.format(palpitatingHeartYouBizManager.buildActivityTimeKey(FOUR_FRAGMENT_USER_KEY), uid);
        Long cartNumber = Optional.ofNullable((Integer) redisManager.hget(key, PalpitaingHeartYouEnum.FourfragmentEnum.FIVE_GIFY_1.getFragmentKey())).orElse(0).longValue();
        for (PalpitaingHeartYouEnum.FourfragmentEnum item : PalpitaingHeartYouEnum.FourfragmentEnum.values()) {
            Long ored = Optional.ofNullable((Integer) redisManager.hget(key, item.getFragmentKey())).orElse(0).longValue();
            cartNumber = ored < cartNumber ? ored : cartNumber;
            log.info("cartNumber min for uid:{}, item:{}, ored:{}", uid, item.getFragmentKey(), ored);
        }
        log.info("end cartNumber:{} for uid:{}", cartNumber, uid);

        if (cartNumber <= 0) {
            log.warn("the user s fragment card exchange is abnormal uid:{}", uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "碎片数量不足，快去获取碎片吧～");
        }
        // 碎片卡 - min
        for (PalpitaingHeartYouEnum.FourfragmentEnum item : PalpitaingHeartYouEnum.FourfragmentEnum.values()) {
            redisManager.hdecr(key, item.getFragmentKey(), cartNumber, DateUtil.ONE_MONTH_SECOND);
            log.info("the number of fragmentation cards is reduced:{} for uid:{}", cartNumber, uid);

            PalpitaingHeartYouEnum.FourfragmentEnum byFragmentKey = PalpitaingHeartYouEnum.FourfragmentEnum.getByFragmentKey(item.getFragmentKey());
            if (Objects.isNull(byFragmentKey)) {
                log.warn("FourfragmentEnum getByFragmentKey is null byFragmentKey item:{}", item);
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }

            // 扣除背包礼物
            List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithBizIdAndType(appId, unionId, uid, byFragmentKey.getGiftKey(), UserPackageBizType.GIFT.getCode(),
                    cartNumber, PackageUseScene.activity.getCode());
            if (CollectionUtils.isEmpty(usePackageList)) {
                throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180020, "资源不足");
            }

            log.info("User backpack, redis deduct fragments uid:{}, byFragmentKey:{}", uid, JSONObject.toJSONString(byFragmentKey));
        }

        // 心动卡 + min
        redisManager.incrLong(String.format(palpitatingHeartYouBizManager.buildActivityTimeKey(HEART_BEAT_KEY), uid), cartNumber, DateUtil.ONE_MONTH_SECOND);
        log.info("the number of heart cards increased :{}, uid:{}", cartNumber, uid);
        palpitatingHeartYouTrackManager.allActivityTaskFinish(uid,"card_composition",cartNumber,null,"cupid_card");
        return cartNumber;
    }


    /******************  定时任务  ******************/


    /**
     * 发放陪伴榜礼物
     *
     * @return
     */
    public Boolean sendAccompanyLeaderboard() {
        // 获取该榜单对应奖品
        // 前三的礼物奖励
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), ACTIVITY_CODE, "rank_accompany");
        log.info("sendAccompanyLeaderboard scenePrizeDOList:{}", scenePrizeDOList);
        // 获取榜单，根据 rank 发放礼物
        RankVO topTenRanks = null;
        try {
            topTenRanks = getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(palpitatingHeartYouBizManager.buildActivityTimeKey(ACCOMPANY_RANK_KEY))
                    .type(RankContext.RankType.user)
                    .build());
        } catch (Exception e) {
            log.warn("获取陪伴榜信息失败", e);
        }

        if (Objects.isNull(topTenRanks)) {
            return Boolean.FALSE;
        }

        List<RankItem> rankList = topTenRanks.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            log.warn("sendAccompanyLeaderboard rankList is null");
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());

            if (rankItem.getValue() < 200000L) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }

            // 发放礼物奖励 + 头像框奖励
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                log.info("uid {} rank {} 没有奖励", rankItem.getId(), rank);
                continue;
            }
            log.info("user accompanyLeaderboard scene PrizeDO List:{} ++  --> uid:{}", scenePrizeDOs, rankItem.getId());
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );

            // 埋点(因为这里一个排名直发放一种礼物)
            ScenePrizeDO scenePrizeDO = scenePrizeDOList.get(0);
            // TODO 领奖怎么传 poolCode
            trackManager.allActivityReceiveAward(rankItem.getId(), scenePrizeDO.getPrizeValue(), "be_with_rank", scenePrizeDO.getPrizeValueGold(), 1);

            // 小助手消息
            String template = getActivityMessageTemplate(ACTIVITY_CODE, PalpitatingHeartYouConstant.ACCOMPANY_RANK);
            String msg =String.format(template, rank);
            notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), rankItem.getId(), msg);
        }

        return Boolean.TRUE;
    }

    private String getActivityMessageTemplate(String activityCode, String accompanyRankKey) {
        String msg = palpitatingHeartYouRedisMangaer.getMessageTemplateCache(activityCode, accompanyRankKey);
        if (CharSequenceUtil.isNotBlank(msg)) {
            return msg;
        }
        ActivityMessageTemplates activityMessageTemplates = activityMessageTemplatesService.findByActivityCodeAndSceneCode(activityCode, accompanyRankKey);
        if (ObjectUtil.isNull(activityMessageTemplates)) {
            return "";
        }
        return activityMessageTemplates.getMessageTemplate();
    }

    /**
     * 发放心动榜礼物
     */
    public Boolean sendHeartbeatLeaderboard() {
        // 前三的礼物奖励
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), ACTIVITY_CODE, "rank_heart");
        log.info("sendHeartbeatLeaderboard scenePrizeDOList:{}", scenePrizeDOList);

        RankVO topTenRanks = null;
        try {
            topTenRanks = getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(palpitatingHeartYouBizManager.buildActivityTimeKey(HEARTBEAT_RANK_KEY))
                    .type(RankContext.RankType.room)
                    .build());
        } catch (Exception e) {
            log.warn("获取梦境剧场榜单信息失败", e);
        }

        if (topTenRanks != null && topTenRanks.getRankList() != null) {
            for (RankItem rankItem : topTenRanks.getRankList()) {
                // 查询房主 uid
                Result<RoomVO> roomResult = feignRoomService.getRoomInfoByRoomId(rankItem.getId(), ServicesAppIdEnum.lanling.getAppId());
                if (!roomResult.success() || Objects.isNull(roomResult.successData())) {
                    log.warn("获取房间信息失败，roomId:{}", rankItem.getId());
                    continue;
                }

                Long roomOwnerId = roomResult.successData().getRoomOwnerId();
                log.info("user heartbeatLeaderboard scene roomOwnerId:{}", roomOwnerId);

                Long value = rankItem.getValue();
                Long rank = rankItem.getRank();
                log.info("user heartbeatLeaderboard scene rank:{} value:{} --> roomId:{} --> uid{}", rank, value, rankItem.getId(), roomOwnerId);

                List<RankItem> rankList = topTenRanks.getRankList();
                if (CollectionUtils.isEmpty(rankList)) {
                    log.warn("heartbeatLeaderboard rankList is null");
                    return Boolean.FALSE;
                }

                List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())).collect(Collectors.toList());

                if (rankItem.getValue() < 500000L) {
                    log.info("roomOwnerId {} rank {} 没有奖励", roomOwnerId, rank);
                    continue;
                }

                // 发放礼物奖励
                if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                    log.info("scenePrizeDOs is null roomOwnerId {} rank {} 没有奖励", roomOwnerId, rank);
                    continue;
                }
                log.info("user heartbeatLeaderboard scene PrizeDO List:{} ++  --> uid:{}", scenePrizeDOs, roomOwnerId);
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(roomOwnerId).build(),
                        scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
                );

                // 埋点(因为这里一个排名直发放一种礼物)
                ScenePrizeDO scenePrizeDO = scenePrizeDOList.get(0);
                trackManager.allActivityReceiveAward(roomOwnerId, scenePrizeDO.getPrizeValue(), "heart_beat_rank", scenePrizeDO.getPrizeValueGold(), 1);

                // 小助手消息

                String template = getActivityMessageTemplate(ACTIVITY_CODE, PalpitatingHeartYouConstant.HEARTBEAT_RANK);
                String msg =String.format(template, rank);
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(), roomOwnerId, msg);

            }
        }
        return Boolean.TRUE;
    }
}
