package cn.yizhoucp.ump.biz.project.biz.manager.activity.lovestory;

import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LoveStoryRankManager extends AbstractRankManager {
    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setSupportDiff(Boolean.TRUE);
        rankContext.setRankLen(10L);
    }

    @Override
    public Boolean sendPrize(RankContext rankContext) {
        return Boolean.FALSE;
    }
}
