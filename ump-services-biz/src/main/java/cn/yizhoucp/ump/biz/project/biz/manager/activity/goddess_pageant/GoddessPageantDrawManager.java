package cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 女神评选抽奖类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 15:19 2025/3/18
 */
@Slf4j
@Service
public class GoddessPageantDrawManager extends AbstractDrawTemplate {

    @Override
    protected void resourceCheck(DrawContext context) {

    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {

    }
}
