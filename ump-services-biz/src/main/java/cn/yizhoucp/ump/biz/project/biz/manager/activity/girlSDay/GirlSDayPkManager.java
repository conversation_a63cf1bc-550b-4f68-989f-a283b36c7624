package cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay;

import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.FeishuNotifyUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.girlSDay.PKResultVO;
import cn.yizhoucp.ump.api.vo.girlSDay.PkInfoVO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay.GirlSDayHelper.PK_LIMIT_VALUE;

@Slf4j
@Service
public class GirlSDayPkManager {

    @Resource
    private GirlSDayHelper girlSDayHelper;

    @Resource
    private GirlSDayRankManager girlSDayRankManager;

    @Resource
    private GirlSDayDataManager girlSDayDataManager;

    @Resource
    private RedisManager redisManager;

    @Resource
    private FeignUserService feignUserService;

    @Resource
    private GirlSDayTrackManager girlSDayTrackManager;

    @Resource
    private GirlSDayGuardManager girlSDayGuardManager;

    public PkInfoVO getPkInfo(Long uid, boolean enter) {
        if (girlSDayHelper.getNowHour() < 20) {
            return null;
        }
        if (!girlSDayDataManager.participatePk(uid)) {
            log.debug("uid:{}未参与pk", uid);
            return null;
        }
        Long pkUser = girlSDayDataManager.getPkUser(uid);
        if (pkUser == null) {
            return null;
        }
        boolean firstEnter = Boolean.FALSE;
        if (enter) {
            firstEnter = redisManager.setnx("ump:girlsday:pk:firstenter:" + uid, System.currentTimeMillis(), 4*RedisManager.ONE_HOUR_SECONDS);
        }
        return PkInfoVO.builder()
                .firstEnter(firstEnter)
                .toUserInfo(feignUserService.getBasic(pkUser, MDCUtil.getCurAppIdByMdc()).successData())
                .myUserInfo(feignUserService.getBasic(uid, MDCUtil.getCurAppIdByMdc()).successData())
                .myValue(girlSDayDataManager.getGuardValueAfter8(uid)*10)
                .toValue(girlSDayDataManager.getGuardValueAfter8(pkUser)*10)
                .changeMyself(Boolean.FALSE)
                .remainSecond(getRemainingSecondsOfToday())
                .myLevel(GirlSDayHelper.GUARD_LEVEL_NAME[Optional.ofNullable(girlSDayGuardManager.getMaxGuardLevel(uid)).orElse(0)])
                .toLevel(GirlSDayHelper.GUARD_LEVEL_NAME[Optional.ofNullable(girlSDayGuardManager.getMaxGuardLevel(pkUser)).orElse(0)])
                .build();
    }
    public long getRemainingSecondsOfToday() {
        LocalDateTime now = girlSDayHelper.getNow();
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        Duration duration = Duration.between(now, endOfDay);
        return duration.getSeconds();
    }

    public PKResultVO getPkResult(Long uid) {
        if (girlSDayHelper.getNowHour() >= 20) {
            return null;
        }
        if (!girlSDayDataManager.yesterdayParticipatePk(uid)) {
            log.debug("uid:{}未参与pk", uid);
            return null;
        }
        if (!girlSDayDataManager.pkResultSuccessFlag()) {
            log.debug("pkResultSuccessFlag未开启");
            return null;
        }
        Boolean firstEnter = redisManager.setnx("ump:girlsday:pk:getPkResult:" + uid, System.currentTimeMillis(), getRemainingSecondsOfToday());
        if (!Boolean.TRUE.equals(firstEnter)) {
            return null;
        }
        Integer pkRewardCount = girlSDayDataManager.getPkRewardCount(uid);
        return PKResultVO.builder()
                .victory(pkRewardCount != null)
                .rewardFairyWandCount(pkRewardCount)
                .build();
    }

    public void generatePk(String date) {
        if (girlSDayHelper.endDay()) {
            log.info("最后一天不生成pk");
            return;
        }
        // 获取昨日pk榜单
        List<Long> yesterdayGuardRankLimitScore = girlSDayRankManager.getGuardRankLimitScore(PK_LIMIT_VALUE, date);
        if (CollectionUtils.isEmpty(yesterdayGuardRankLimitScore)) {
            log.warn("昨日pk榜单为空, date -> {}", date);
            return;
        }
        girlSDayDataManager.batchAddPkUser(yesterdayGuardRankLimitScore);
        Long leftUser = null;
        int count = 0;
        for (int i = 0; i < yesterdayGuardRankLimitScore.size(); i++) {
            if (i % 2 == 0) {
                leftUser = yesterdayGuardRankLimitScore.get(i);
            } else {
                Long rightUser = yesterdayGuardRankLimitScore.get(i);
                if (leftUser != null) {
                    girlSDayDataManager.addPkUser(leftUser, rightUser);
                    girlSDayDataManager.addPkUser(rightUser, leftUser);
                    count ++;
                }
            }
        }
        log.info("pk信息生成成功, count -> {}", count);
        FeishuNotifyUtil.feishuNotify(String.format("pk信息生成成功, count -> %s", count), "52d8ce5a-1a63-4585-be36-11b89848d9f3");
    }

    public void pkResult() {
        Map<Long, Long> allPkUser = girlSDayDataManager.allYesterdayPkUser();
        if (CollectionUtils.isEmpty(allPkUser)) {
            return;
        }
        Set<String> allUser = new HashSet<>(allPkUser.size());
        allPkUser.forEach((u1, u2) -> {
            allUser.add(AppUtil.splicUserId(u1, u2));
        });
        int wandCount = 0;
        for (String s : allUser) {
            List<Long> longs = AppUtil.openSplicUserId(s);
            Long u1 = longs.get(0);
            Long u2 = longs.get(1);
            Long u1Value = girlSDayDataManager.getYesterdayGuardValueAfter8(u1);
            Long u2Value = girlSDayDataManager.getYesterdayGuardValueAfter8(u2);
            Long rewardUid = null;
            if (u1Value > u2Value) {
                rewardUid = u1;
            } else if (u1Value < u2Value) {
                rewardUid = u2;
            }
            if (rewardUid == null) {
                log.warn("pkResult -> u1:{} u2:{} u1Value:{} u2Value:{}", u1, u2, u1Value, u2Value);
                continue;
            }
            Integer rewardCount = getPkRewardCount(rewardUid);
            if (rewardCount == null) {
                log.warn("pkResult -> u1:{} u2:{} u1Value:{} u2Value:{}", u1, u2, u1Value, u2Value);
                continue;
            }
            log.info("pkResult -> u1:{} u2:{} u1Value:{} u2Value:{} rewardUid:{} rewardCount:{}", u1, u2, u1Value, u2Value, rewardUid, rewardCount);
            girlSDayDataManager.addPkWin(rewardUid, rewardCount);
            girlSDayDataManager.incrFairyWand(rewardUid, rewardCount);
            log.info("reward wand uid -> {}, count -> {}", rewardUid, rewardCount);
            girlSDayTrackManager.finishPk(u1, u2, u1Value, u2Value, rewardCount);
            wandCount += rewardCount;
        }
        girlSDayDataManager.pkResultSuccess();
        FeishuNotifyUtil.feishuNotify(String.format("pk奖励下发完毕, count -> %s", wandCount), "52d8ce5a-1a63-4585-be36-11b89848d9f3");
    }

    private Integer getPkRewardCount(Long uid) {
        Long rank = girlSDayRankManager.getGuardRankByUid(uid);
        if (rank == null) {
            return null;
        }
        if (rank <= 10) {
            return 10;
        } else if (rank <= 50) {
            return 6;
        } else if (rank <= 100) {
            return 4;
        } else if (rank <= 200) {
            return 2;
        } else {
            return 1;
        }
    }
}
