package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.buoyBar.ChatBarVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.BuoyBarManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class MainChineseNewYear2025BuoyManager implements BuoyBarManager {
    @Resource
    private ActivityStatusManager activityStatusManager;
    @Resource
    private MainChineseNewYear2025BizManager mainChineseNewYear2025BizManager;

    @Override
    public ChatBarVO getBuoyBar(BaseParam param, Long toUid) {
        if (Objects.isNull(param.getUid()) || Objects.isNull(toUid)) {
            return ChatBarVO.builder().isShow(Boolean.FALSE).build();
        }

        Boolean enable = activityStatusManager.activityIsEnable(param, MainChineseNewYear2025Constant.ACTIVITY_CODE);
        if (!Boolean.TRUE.equals(enable)) {
            return ChatBarVO.builder().isShow(Boolean.FALSE).build();
        }

        return ChatBarVO.builder()
                .isShow(Boolean.TRUE)
                .icon("https://res-cdn.nuan.chat/admin-v2/files/dev/2025-01/f613aa4593e3e3f8e00cc4013f641cff.png")
                .href(mainChineseNewYear2025BizManager.getActivityUrl(param, "chinese_new_year_2025_main") + "&toUid=" + toUid)
                .value(0)
                .maxValue(0)
                .toUid(toUid)
                .valueName("")
                .build();
    }




}
