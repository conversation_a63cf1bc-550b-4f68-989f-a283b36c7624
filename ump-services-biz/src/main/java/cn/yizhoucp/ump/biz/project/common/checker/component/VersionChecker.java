package cn.yizhoucp.ump.biz.project.common.checker.component;

import cn.yizhoucp.ms.core.base.enums.PlatformEnum;
import cn.yizhoucp.ms.core.base.enums.VestChannelEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.VersionUtil;
import cn.yizhoucp.ump.biz.project.common.checker.BaseChecker;
import cn.yizhoucp.ump.biz.project.common.checker.CheckerContext;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component("versionChecker")
public class VersionChecker extends BaseChecker {
    @Override
    protected Boolean doCheck(CheckerContext context, JSONObject param) {
        if (VestChannelEnum.nuanliao.equals(MDCUtil.getCurVestChannelEnumByMdc())) {
            return checkVersion(MDCUtil.getVersionNameByMdc(), context.getNuanliaoVersion());
        }
        if (VestChannelEnum.huayuan.equals(MDCUtil.getCurVestChannelEnumByMdc())) {
            return checkVersion(MDCUtil.getVersionNameByMdc(), context.getHuayuanVersion());
        }
        if (VestChannelEnum.lianainiang.equals(MDCUtil.getCurVestChannelEnumByMdc())) {
            String version = PlatformEnum.iOS.equals(MDCUtil.getUserPlatform()) ? context.getLianainiangIOSVersion() : context.getLianainiangVersion();
            return checkVersion(MDCUtil.getVersionNameByMdc(), version);
        }
        if (VestChannelEnum.yumo.equals(MDCUtil.getCurVestChannelEnumByMdc())) {
            return checkVersion(MDCUtil.getVersionNameByMdc(), context.getYumoVersion());
        }
        return Boolean.TRUE;
    }

    private Boolean checkVersion(String versionNameByMdc, String version) {
        if (version == null || versionNameByMdc == null) {
            log.info("version is null -> {}, -> {}", version, versionNameByMdc);
            return Boolean.TRUE;
        }
        return VersionUtil.isNewVersion(version, versionNameByMdc);
    }
}
