package cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.common;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityRedisKeyGenerator;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;


/**
 * 紫藤萝之恋 redis管理类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:05 2025/4/18
 */
@Component
public class WisteriaLove1RedisManager {
    @Resource
    private RedisManager redisManager;
    @Resource
    private ActivityRedisKeyGenerator activityRedisKeyGenerator;
    @Resource
    private UserFeignManager userFeignManager;

    public String getActivityCode() {
        return WisteriaLove1Constant.ACTIVITY_CODE;
    }


    public Boolean getIsFreeRefresh(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(WisteriaLove1Constant.IS_FREE_REFRESH, getActivityCode(), uid.toString(), DateUtil.getNowYyyyMMdd());
        return redisManager.setnx(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public List<ScenePrizeDO> getShortGiftList(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(WisteriaLove1Constant.SHORT_GIFT_LIST, getActivityCode(), uid.toString());
        String jsonStr = redisManager.getString(key);
        if (jsonStr == null) {
            return null;
        }
        return JSON.parseArray(jsonStr, ScenePrizeDO.class);
    }

    public void refreshShortGiftList(Long uid, List<ScenePrizeDO> newGifts) {
        // 更新Redis中的商品列表
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                WisteriaLove1Constant.SHORT_GIFT_LIST,
                getActivityCode(),
                uid.toString()
        );
        redisManager.set(key, JSON.toJSONString(newGifts), DateUtil.ONE_MONTH_SECOND);
    }


    public Integer getGiftPurchasedCount(Long uid, String prizeValue) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(WisteriaLove1Constant.GIFT_PURCHASED_COUNT, getActivityCode(), uid.toString(), prizeValue, DateUtil.getNowYyyyMMdd());
        return Optional.ofNullable(redisManager.getInteger(key)).orElse(0);
    }

    public Long incrementShortRefreshCount(Long uid){
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(WisteriaLove1Constant.SHORT_REFRESH_COUNT, getActivityCode(), uid.toString(), DateUtil.getNowYyyyMMdd());
        return redisManager.incrLong(key, 1, DateUtil.ONE_MONTH_SECOND);
    }


    public void recoredBroadCast(Long uid, List<ScenePrizeDO> newGifts) {
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        if (ObjectUtil.isNotNull(userVO)) {
            String brodCastRedisKey = activityRedisKeyGenerator.generateKeyWithActivityStartTime(WisteriaLove1Constant.BROADCAST, WisteriaLove1Constant.ACTIVITY_CODE);
            for (ScenePrizeDO scenePrizeDO : newGifts) {
                HashMap<String, String> broadCast = new HashMap<>();
                broadCast.put("userName", userVO.getName());
                broadCast.put("itemName", scenePrizeDO.getPrizeDesc());
                redisManager.listLpush(brodCastRedisKey, JSON.toJSONString(broadCast), cn.yizhoucp.ms.core.base.util.DateUtil.ONE_MONTH_SECOND);
            }
            redisManager.listTrim(brodCastRedisKey, 0, 20);
        }
    }

    public JSONArray getBrodCast() {
        String brodCastRedisKey = activityRedisKeyGenerator.generateKeyWithActivityStartTime(WisteriaLove1Constant.BROADCAST, WisteriaLove1Constant.ACTIVITY_CODE);
        List<Object> redisResult = redisManager.lGet(brodCastRedisKey, 0, 20);
        if (redisResult == null || redisResult.isEmpty()) {
            return null;
        }
        return JSON.parseArray(String.valueOf(redisResult));
    }
}
