package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 女神培训-报名记录
 *
 * @author: dongming
 */
@Table(name = "goddess_train_sign_up_record")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoddessTrainSignUpRecordDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 应用唯一标识 */
    private String unionId;
    /** 应用 ID */
    private Long appId;
    /** 用户id */
    private Long uid;
    /** 活动编号 */
    private String activityCode;
    /** 活动名称 */
    private String activityName;
    /** 学习内容 */
    private String learnList;
    /** 主要问题 */
    private String mainProblem;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新时间 */
    private LocalDateTime updateTime;

}
