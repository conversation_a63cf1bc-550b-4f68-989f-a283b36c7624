package cn.yizhoucp.ump.biz.project.biz.enums.activity.lanternFestival2025;

/**
 * <AUTHOR>
 * @description
 * @create 2025-01-13
 **/
public enum LanternRedisEnum {
    lantern_riddle_record("ump:lantern_riddle_record_%s", "用户猜灯谜记录(hash)"),
    lantern_user_lantern_count("ump:lantern_user_lantern_count", "用户花灯数量(hash)"),
    lantern_riddle_receive_record("ump:lantern_riddle_receive_record_%s", "灯谜奖励领取记录(hash)"),
    lantern_mission_info("ump:lantern_mission_info_%s_%s", "任务信息(hash)"),
    lantern_user_yuanxiao_count("ump:lantern_user_yuanxiao_count_%s", "用户元宵数量(hash)"),
    lantern_activity_luck_total_list("ump:lantern_activity_luck_total_list", "元宵活动福气值总榜(zSet)"),
    lantern_user_friend_relation("ump:lantern_user_friend_relation", "用户好友关系(hash)"),
    lantern_love_answer("ump:lantern_love_answer", "爱情故事灯谜答案(hash)"),
    ;

    private String key;

    private String desc;

    LanternRedisEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
