package cn.yizhoucp.ump.biz.project.biz.manager.activity.lover1v1;

import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.imservices.ImChatInfoVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.ActivityVO;
import cn.yizhoucp.ump.api.vo.jimu.buoyBar.ChatBarVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.BuoyBarManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.JimuManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-01-10 17:25
 */
@Component
@Slf4j
public class WinterLoverBuoyBarManager implements BuoyBarManager {

    @Resource
    private FeignImService feignImService;

    @Resource
    private JimuManager jimuManager;

    @Resource
    private Environment environment;

    @Value("${activity.h5}")
    private String activityH5;

    private final static String ACTIVITY_CODE = "winter-lovers";


    private final static String BOUY_HREF = "winter-love?chatId";


    @Override
    public ChatBarVO getBuoyBar(BaseParam param, Long toUid) {
        Long uid = param.getUid();
        Long appId = param.getAppId();
        ImChatInfoVO imChatInfoVO = feignImService.getChat(appId, uid, toUid).successData();
        ActivityVO activityVO = jimuManager.activityInfo(ACTIVITY_CODE, param);
        String url;
        Boolean isShow = null != activityVO && activityVO.getStatus() == 1;
        String baseUrl = activityH5.endsWith("/") ? String.format(activityH5, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) : (String.format(activityH5, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "/");

        if (imChatInfoVO != null || Objects.equals(toUid, SystemNPC.LANLING_LITTLE.getUserId())) {
            Long chatId = imChatInfoVO.getId();

            if (chatId != null) {
                url = baseUrl.concat(BOUY_HREF.concat("=").concat(chatId.toString()));
            } else {
                url = baseUrl.concat(BOUY_HREF);
            }
        } else {
            url = baseUrl.concat(BOUY_HREF);

            log.warn("getBouyBar imChatInfoVO is empty uid:{} ,toUid : {}", uid, toUid);
            isShow = false;
        }
        return ChatBarVO.builder()
                .isShow(Boolean.TRUE)
                .icon("https://res-cdn.nuan.chat/res/activity/heartBeat/winter_lover_buoy_bar.png")
                .href(url)
                .value(null)
                .maxValue(5200)
                .toUid(toUid)
                .isShow(isShow)
                .valueName("甜蜜值")
                .build();
    }
}
