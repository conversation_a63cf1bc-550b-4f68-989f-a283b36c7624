package cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonRankListParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret.common.TlsRedisControls;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractButtonManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret.common.TlsConstant.*;

@Service
@Slf4j
public class TreasureInSecretButtonManager extends AbstractButtonManager {

    @Resource
    private TreasureInSecretRankManager rankManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private TreasureInSecretSupperManager treasureInSecretSupperManager;
    @Resource
    private TlsRedisControls controls;
    @Resource
    private RedisManager redisManager;

    @Override
    public Object event(ButtonEventParam buttonEventParam) {
        log.info("TreasureInSecretButtonManager event :{},buttonEventParam:{} ", buttonEventParam.getEventCode(), buttonEventParam);
        if (!buttonEventParam.check() || !buttonEventParam.getBaseParam().check()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        switch (buttonEventParam.getEventCode()) {
            case OPEN_BOX:
                return treasureInSecretSupperManager.openBox(buttonEventParam);
            case CLAIM_CARD:
                return treasureInSecretSupperManager.claimCard(buttonEventParam);
            case EXCHANGE:
                return treasureInSecretSupperManager.exchange(buttonEventParam);

            default:
                log.error("eventCode is not support:{}", buttonEventParam.getEventCode());
                throw new ServiceException(ErrorCode.INVALID_PARAM, "eventCode is not support");
        }
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("TreasureInSecretButtonManager sendGiftHandle event:{}", event);
        this.sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }


    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        // 获取送的礼物
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            // 数据校验
            if (Objects.isNull(coinGiftGivedModel.getFromUid())) {
                log.warn("treasure_in_secret: fromUid is null");
                continue;
            }
            log.info("treasure_in_secret init sendGiftHandle coinGiftGivedModel:{} for fromId:{} --> toUid:{}", coinGiftGivedModel, coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid());
            // 处理用户互相赠送卡
            if (treasureInSecretSupperManager.giveCard(coinGiftGivedModel)) {
                continue;
            }

            // 校验
            if (!checkPanelGift(coinGiftGivedModel)) {
                continue;
            }

            // 处理任务
            processTask(coinGiftGivedModel);
            countGiftsSent(coinGiftGivedModel);
            countGiftsReceived(coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }


    /**
     * 获取活动礼物集合
     */
    public List<ScenePrizeDO> giveOutGlamourLists() {
        String key = String.format(ACTIVITY_TASK_GIFT_LIST, controls.buildKeyPrefix());
        if (redisManager.hasKey(key)) {
            return JSON.parseArray(redisManager.getString(key), ScenePrizeDO.class);
        }

        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "task");
        log.info("giveOutGlamourLists scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Collections.emptyList();
        }

        redisManager.set(key, JSON.toJSONString(scenePrizeDOList), DateUtil.ONE_MONTH_SECOND);
        return scenePrizeDOList;
    }

    /**
     * 处理任务
     *
     * @param coinGiftGivedModel
     * @return
     */
    private void processTask(CoinGiftGivedModel coinGiftGivedModel) {
        // 获取礼物
        Long uid = coinGiftGivedModel.getFromUid();
        String giftKey = coinGiftGivedModel.getGiftKey();
        Long productCount = coinGiftGivedModel.getProductCount();

        List<ScenePrizeDO> scenePrizeDOList = giveOutGlamourLists();
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.error("treasure_in_secret: giveOutGlamourLists is empty");
            return;
        }
        Map<String, Integer> map = scenePrizeDOList.stream()
                .collect(Collectors.toMap(
                        ScenePrizeDO::getPrizeValue,
                        ScenePrizeDO::getPrizeNum
                ));

        log.debug("eligible gifts map {}", JSON.toJSONString(map));
        if (!map.containsKey(giftKey)) {
            return;
        }

        Integer num = map.get(giftKey);
        log.info("processTask eligible gifts giftKey {} -> num:{} ", giftKey, num);
        controls.addUserRewardHunt(uid, num.longValue() * productCount);
    }

    /**
     * 统计送礼榜
     */
    public void countGiftsSent(CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getGiftKey();
        Long fromUid = coinGiftGivedModel.getFromUid();
        Long coin = coinGiftGivedModel.getCoin();
        if (StringUtils.isEmpty(giftKey) || Objects.isNull(fromUid) || Objects.isNull(coin)) {
            return;
        }
        // 只活动任务礼物
        List<ScenePrizeDO> scenePrizeDOList = giveOutGlamourLists();
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.error("countGiftsSent treasure_in_secret: giveOutGlamourLists is empty");
            return;
        }
        List<String> collect = scenePrizeDOList.stream()
                .map(ScenePrizeDO::getPrizeValue)
                .collect(Collectors.toList());

        if (!collect.contains(giftKey)) {
            return;
        }

        rankManager.incrRankValue(fromUid, coin * 10, controls.keySendingKey());
    }

    /**
     * 统计收礼榜
     */
    public void countGiftsReceived(CoinGiftGivedModel coinGiftGivedModel) {
        String giftKey = coinGiftGivedModel.getGiftKey();
        Long toUid = coinGiftGivedModel.getToUid();
        Long coin = coinGiftGivedModel.getCoin();
        if (StringUtils.isEmpty(giftKey) || Objects.isNull(toUid) || Objects.isNull(coin)) {
            return;
        }
        // 不要活动任务礼物
        List<ScenePrizeDO> scenePrizeDOList = giveOutGlamourLists();
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.error("countGiftsReceived treasure_in_secret: giveOutGlamourLists is empty");
            return;
        }
        List<String> collect = scenePrizeDOList.stream()
                .map(ScenePrizeDO::getPrizeValue)
                .collect(Collectors.toList());

        if (collect.contains(giftKey)) {
            return;
        }
        log.info("countGiftsReceived toUid:{} giftKey:{} coin:{}", toUid, giftKey, coin);

        // 将 long 转换为 BigDecimal
        BigDecimal coinBD = new BigDecimal(coin);

        // 定义除数 5
        BigDecimal divisor = new BigDecimal("5");
        // 执行除法并四舍五入
        BigDecimal result = coinBD.divide(divisor, RoundingMode.HALF_UP);

        // 将结果转换回 Long
        Long roundedResult = result.longValue();
        log.debug("countGiftsReceived toUid:{} giftKey:{} coin:{} result:{}", toUid, giftKey, coin, roundedResult);
        rankManager.incrRankValue(toUid, roundedResult, controls.keyReceivingKey());

        // 用户自己的珍宝值
        controls.addUserGemValue(toUid, roundedResult);
    }

    /**
     * 校验
     *
     * @param coinGiftGivedModel
     * @return
     */
    private Boolean checkPanelGift(CoinGiftGivedModel coinGiftGivedModel) {
        // 面板礼物
        if (StringUtils.isEmpty(coinGiftGivedModel.getGiftWay()) || !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getGiftWay(), GiftWay.NORMAL.getCode())) {
            return Boolean.FALSE;
        }

        // 只要语音房的
        if (StringUtils.isEmpty(coinGiftGivedModel.getFrom()) ||
                (!StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.room.getCode()) &&
                        !StringUtils.equalsIgnoreCase(coinGiftGivedModel.getFrom(), GiftFrom.gift_in_return_voice_room.getCode()))) {
            return Boolean.FALSE;
        }

        log.info("eligible gifts coinGiftGivedModel:{}", coinGiftGivedModel);
        return Boolean.TRUE;
    }




    @Override
    public RankVO rankList(ButtonRankListParam buttonFriendPaream) {
        String listCode = buttonFriendPaream.getListCode();
        if (StringUtils.isEmpty(listCode)) {
            log.warn("listCode is empty");
            return null;
        }
        buttonFriendPaream.setSupportDiff(Boolean.TRUE);
        buttonFriendPaream.setRankLen(10L);
        buttonFriendPaream.setListType(RankContext.RankType.user.getType());

        if (StringUtils.equals(listCode, SEND_KEY)) {
            buttonFriendPaream.setRankKey(controls.keySendingKey());
        } else if (StringUtils.equals(listCode, RECEIVE_KEY)) {
            buttonFriendPaream.setRankKey(controls.keyReceivingKey());
        } else {
            log.warn("listCode is not support:{}", listCode);
            return null;
        }

        return super.rankList(buttonFriendPaream);
    }


}
