package cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 公主日记排行榜类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 11:34 2025/3/3
 */
@Slf4j
@Service
public class ThePrincessDiariesRankManager extends AbstractRankManager {

    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private ThePrincessDiariesRedisManager thePrincessDiariesRedisManager;

    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {

    }

    public Boolean sendAllRankPrize() {
        for (ThePrincessDiariesEnums.RankEnum rankEnum : ThePrincessDiariesEnums.RankEnum.values()) {
            this.sendRankPrize(rankEnum.getRankCode());
        }
        return Boolean.TRUE;
    }

    public Boolean sendRankPrize(String rankCode) {
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ThePrincessDiariesConstant.ACTIVITY_CODE, rankCode);
        log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return Boolean.FALSE;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ThePrincessDiariesConstant.ACTIVITY_CODE)
                .rankKey(thePrincessDiariesRedisManager.getRankKey(rankCode))
                .rankLen(10L)
                .type(RankContext.RankType.user)
                .build());
        log.info("rankVO {}", rankVO);
        if (rankVO == null) {
            return Boolean.FALSE;
        }

        List<RankItem> rankList = rankVO.getRankList();
        if (CollectionUtils.isEmpty(rankList)) {
            return Boolean.FALSE;
        }

        for (RankItem rankItem : rankList) {
            Long rank = rankItem.getRank();
            Long value = rankItem.getValue();
            if (value < 1314000L) {
                log.info("rank:{} 小于最小奖励值 {}", rankItem, 1314000L);
                continue;
            }
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            log.info("SeasonalFestival#sendPrize cpRankItem {}", rankItem);
            if (CollUtil.isEmpty(scenePrizeDOs)) {
                continue;
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(rankItem.getId()).build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );
            String msg = "恭喜在「公主日记」活动中，榜单排名%s名，获取%s礼物一个，礼物已经下发至背包，快去查看吧～";
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    rankItem.getId(),
                    String.format(msg, rankItem.getRank(), scenePrizeDOs.get(0).getPrizeDesc())
            );
            //埋点
        }

        return Boolean.TRUE;
    }

}
