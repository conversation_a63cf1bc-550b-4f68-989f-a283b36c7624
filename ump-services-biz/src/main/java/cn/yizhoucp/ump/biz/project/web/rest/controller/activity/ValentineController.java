package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.valentine.ValentineAdventManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.valentine.ValentineRomanceManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.umpServices.activity.Valentine.DrawRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: ValentineController
 * @Description: 情人节活动-浪漫告白 Controller
 * @author: pepper
 * @date: 2022/1/18 17:40
 */
@Slf4j
@RestController
public class ValentineController {

    @Resource
    private ValentineRomanceManager romanceManager;

    @Resource
    private ValentineAdventManager adventManager;

    /**
     * 浪漫告白页面
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/page-info")
    public Result romancePageInfo() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.pageInfo()
        );
    }

    /**
     * 发送告白信
     *
     * @param toUid
     * @param content
     * @param type    wall告白墙、full 全服
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/send-confession")
    public Result sendConfession(Long toUid, String content, String type) {

        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.sendConfession(toUid, content, type)
        );
    }

    /**
     * 热度值榜单
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/get-hot-rank")
    public Result getHotRank() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.getHotRank()
        );
    }

    /**
     * 甜蜜值榜单
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/get-sweet-rank")
    public Result getSweetRank() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.getSweetRank()
        );
    }

    /**
     * 获取告白弹幕列表
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/get-praise-list")
    public Result getPraiseList() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.getPraiseList()
        );
    }

    /**
     * 点赞弹幕
     *
     * @param messageId
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/compliments")
    public Result compliments(String messageId) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.compliments(messageId)
        );
    }

    /**
     * 获取告白信息列表
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/get-user-letter")
    public Result getUserLetter() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.getLetterList()
        );
    }

    /**
     * 活动开启
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/activity-on")
    public Result activityOn() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.onActivity()
        );
    }

    /**
     * 活动关闭
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/activity-off")
    public Result activityOff() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.offActivity()
        );
    }

    /**
     * 查看活动状态
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/activity-status")
    public Result activityStatus() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.activityStatus()
        );
    }

    /**
     * 00:00-11:59,12:00-17:59,18:00-23:59
     * 定时任务,热度榜礼物发放
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/sendGiftByPeriod")
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    public Result<Boolean> sendGiftByPeriod() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.sendGiftByPeriod()
        );
    }

    /**
     * 活动结束后
     * 甜蜜榜礼物发放
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/sendGiftBySweet")
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    public Result<Boolean> sendGiftBySweet() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.sendGiftBySweet()
        );
    }

    /**
     * 定时任务，每天晚上清空告白次数
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/clear")
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    public Result<Boolean> clear() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.clear()
        );
    }

    /**
     * 抽奖
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/draw")
    public Result valentineAdventDraw(Integer times) {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> adventManager.draw(times)
        );
    }

    /**
     * 获取抽奖广播列表
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/barrage")
    public Result getBarrage() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> adventManager.getBarrage()
        );
    }

    /**
     * 抽奖页面
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/page-info")
    public Result getAdventPageInfo() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> adventManager.getAdventPageInfo()
        );
    }

    /**
     * 抽奖记录
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/advent/draw-record")
    public Result<DrawRecordVO> getDrawRecord() {
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> adventManager.drawRecord()
        );
    }


    /**
     * 手动送礼
     *
     * @return
     */
    @RequestMapping("/api/inner/activity/valentine/romance/give-gift")
    public Result giveGift(String key, long count, long from, long to) {
        List<CoinGiftGivedModel> list = new ArrayList<>();
        CoinGiftGivedModel coinGiftGivedModel = new CoinGiftGivedModel();

        coinGiftGivedModel.setGiftKey(key);
        coinGiftGivedModel.setProductCount(count);
        coinGiftGivedModel.setFromUid(from);
        coinGiftGivedModel.setToUid(to);
        list.add(coinGiftGivedModel);
        return RestBusinessTemplate.executeWithoutTransaction(
                () -> romanceManager.giveGift(list)
        );
    }
}
