package cn.yizhoucp.ump.biz.project.biz.manager.activity.dragon_Boat_Festival_2025;

import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 2025端午节排行榜类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 17:21 2025/5/29
 */
@Slf4j
@Service
public class DragonBoatFestival2025RankManager extends AbstractRankManager {
    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {

    }
}
