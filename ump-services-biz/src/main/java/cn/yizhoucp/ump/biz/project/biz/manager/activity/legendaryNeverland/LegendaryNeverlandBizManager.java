package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.enums.dressup.WearTypeEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland.common.LnEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland.common.LnRedisControls;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.LnSpiritAnimal;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.LnUserSpiritAnimal;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.LnSpiritAnimalService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.LnUserSpiritAnimalService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland.common.LnConstant.*;

@Service
@Slf4j
public class LegendaryNeverlandBizManager implements ActivityComponent {

    @Autowired
    private LnRedisControls lnRedisControls;
    @Autowired
    private LegendaryNeverlandTrackManager lnTrackManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private LnUserSpiritAnimalService lnUserSpiritAnimalService;
    @Autowired
    private LnSpiritAnimalService lnSpiritAnimalService;
    @Autowired
    private LegendaryNeverlandButtonManager lnButtonManager;


    // 分页查询每一页条数
    public static final Integer PAGE_SIZE = 100;
    // 定位页数Key
    private static final String CURRENT_PAGE_COUNT = "ump:legendary_neverland:current_page_count";

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("LegendaryNeverland sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }

    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        log.info("LegendaryNeverland sendGiftHandle param:{}, coinGiftGivedModelList:{}", param, coinGiftGivedModelList);
        // 获取送的礼物
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            log.info("LegendaryNeverland sendGiftHandle coinGiftGivedModel:{} for fromId:{} --> toUid:{}", coinGiftGivedModel, coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid());
            if (filterBackpackGift(coinGiftGivedModel)) {
                continue;
            }
            log.info("over LegendaryNeverland sendGiftHandle coinGiftGivedModel:{} for fromId:{} --> toUid:{}", coinGiftGivedModel, coinGiftGivedModel.getFromUid(), coinGiftGivedModel.getToUid());

            Long uid = coinGiftGivedModel.getFromUid();
            Long coin = coinGiftGivedModel.getCoin();
            Long productCount = coinGiftGivedModel.getProductCount();
            if (Objects.isNull(productCount) || productCount <= 0) {
                log.warn("sendGiftHandle: productCount is null or <= 0");
                continue;
            }
            // 礼物价值金币
            Long giftCoin = coin / productCount;
            LnEnum.TaskEnum taskEnum = LnEnum.TaskEnum.getByGiftValue(giftCoin);
            if (Objects.nonNull(taskEnum)) {
                Boolean finishTask = lnRedisControls.finishTask(taskEnum.getTaskKey(), uid);
                if (finishTask) {
                    log.info("sendGiftHandle: finishTask success, taskEnum:{}, uid:{}", taskEnum, uid);
                    // 埋点
                    lnTrackManager.allActivityTaskFinish(uid, taskEnum.getTaskKey());
                }
            }
        }

        return true;
    }

    /**
     * 过滤背包礼物
     *
     * @return
     */
    private Boolean filterBackpackGift(CoinGiftGivedModel coinGiftGivedModel) {
        // 数据校验
        if (Objects.isNull(coinGiftGivedModel.getFromUid()) ||
                StringUtils.isBlank(coinGiftGivedModel.getGiftKey()) ||
                Objects.isNull(coinGiftGivedModel.getCoin()) ||
                Objects.isNull(coinGiftGivedModel.getProductCount()) ||
                StringUtils.isBlank(coinGiftGivedModel.getGiftWay())) {
            log.info("sendGiftHandle: argument is null");
            return true;
        }
        String giftWay = coinGiftGivedModel.getGiftWay();
        // 面板礼物
        if (StringUtils.isEmpty(giftWay) || !StringUtils.equalsIgnoreCase(giftWay, GiftWay.NORMAL.getCode())) {
            return true;
        }
        return false;
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    /**
     * 每天 0 点扣除灵灵兽体力
     */
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void deductZeroStrength(Integer poolSize) {
        Long totalPage = getTotalPage();

        long startPage = Optional.ofNullable(redisManager.getLong(CURRENT_PAGE_COUNT)).orElse(-1L);
        if (startPage < 0L) {
            redisManager.set(CURRENT_PAGE_COUNT, 1L, DateUtil.ONE_DAY_SECONDS);
            this.deductStrengthImport(poolSize);
        } else if (startPage > totalPage) {
            return;
        } else {
            this.deductStrengthImport(poolSize);
        }
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void deductStrengthImport(Integer poolSize) {
        Long totalPage = getTotalPage();
        long startPage = redisManager.getLong(CURRENT_PAGE_COUNT);
        if (startPage > totalPage) {
            log.info("deduct Strength allData Import end");
            return;
        }
        ExecutorService executor = Executors.newFixedThreadPool(poolSize);
        // 提交任务到线程池
        for (int i = 0; i < poolSize; i++) {
            executor.submit(new DataProcessor(startPage));
            startPage = redisManager.incrLong(CURRENT_PAGE_COUNT, 1L, DateUtil.ONE_HOUR_SECONDS);
        }

        // 等待所有线程处理完成
        executor.shutdown();
        try {
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted while waiting for threads to complete", e);
        }
        this.deductStrengthImport(poolSize);
    }


    public class DataProcessor implements Runnable {

        private final Long startPage;

        public DataProcessor(Long startPage) {
            this.startPage = startPage;
        }

        @Override
        public void run() {
            IPage<LnUserSpiritAnimal> doPage = new Page<>(startPage, PAGE_SIZE);

            LambdaQueryWrapper<LnUserSpiritAnimal> gted = new LambdaQueryWrapper<LnUserSpiritAnimal>()
                    .isNotNull(LnUserSpiritAnimal::getStamina);

            List<LnUserSpiritAnimal> list = lnUserSpiritAnimalService.page(doPage, gted).getRecords();
            if (Objects.isNull(list) || list.isEmpty()) {
                log.warn("paging queries are null for list start page: {}", startPage);
                return;
            }
            log.info("deductStrengthImport start page: {}, total page size: {}", startPage, list.size());
            // 1. 体力 > 1 的灵兽扣减 1 体力
            List<LnUserSpiritAnimal> collectGtOne = Optional.of(list.stream()
                            .filter(lnUserSpiritAnimal -> lnUserSpiritAnimal.getStamina() >= 1)
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            // 2. 体力为0的灵兽卸下装扮
            List<LnUserSpiritAnimal> collectEqZero = Optional.of(list.stream()
                            .filter(lnUserSpiritAnimal -> lnUserSpiritAnimal.getStamina() == 0)
//                            .filter(lnUserSpiritAnimal -> WearTypeEnum.put_on.getCode().equals(lnUserSpiritAnimal.getCarryType()))
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            log.debug("deductStrengthImport collectGtOne size: {}", collectGtOne.size());
            log.debug("deductStrengthImport collectEqZero size: {}", collectEqZero.size());
            try {
                // 扣减体力
                if (!collectGtOne.isEmpty()) {
                    collectGtOne.forEach(lnUserSpiritAnimal -> {
                        if (!lnUserSpiritAnimalService.deductStamina(lnUserSpiritAnimal.getUserId(), lnUserSpiritAnimal.getUniqueKey(), 1L)) {
                            log.warn("deductStrengthImport error deduct stamina: {}", lnUserSpiritAnimal);
                        }
                    });
                }

                // 卸下装扮
                if (!collectEqZero.isEmpty()) {
                    collectEqZero.forEach(lnUserSpiritAnimal -> {
                        LambdaUpdateWrapper<LnUserSpiritAnimal> wrapper = new LambdaUpdateWrapper<>();
                        wrapper.eq(LnUserSpiritAnimal::getUniqueKey, lnUserSpiritAnimal.getUniqueKey())
                                .eq(LnUserSpiritAnimal::getUserId, lnUserSpiritAnimal.getUserId())
                                .set(LnUserSpiritAnimal::getCarryType, WearTypeEnum.take_off.getCode())
                                .set(LnUserSpiritAnimal::getUpdateTime, new Date());
                        if (!lnUserSpiritAnimalService.update(wrapper)) {
                            log.warn("deductStrengthImport error update carryType: {}", lnUserSpiritAnimal);
                        }

                        // 卸下
                        LnSpiritAnimal spiritAnimal = lnButtonManager.allSpiritAnimalList().get(lnUserSpiritAnimal.getUniqueKey());
                        if (Objects.nonNull(spiritAnimal)) {
                            lnButtonManager.unloaded(lnUserSpiritAnimal.getUserId(), spiritAnimal, WearTypeEnum.take_off.getCode());
                        }
                    });
                }
            } catch (Exception e) {
                log.warn("all deductStrengthImport error: {}", e.toString());
            }
        }

    }

    /**
     * 获取总分页数
     */
    public Long getTotalPage() {
        QueryWrapper<LnUserSpiritAnimal> queryWrapper = new QueryWrapper<>();
        return lnUserSpiritAnimalService.count(queryWrapper) / PAGE_SIZE + 1L;
    }

    /**
     * 获取用户当前连送特效背景
     */
    public String getUserNrgcKey(Long uid) {
        log.info("getUserNrgcKey uid: {}", uid);
        List<LnUserSpiritAnimal> list = Optional.ofNullable(lnUserSpiritAnimalService.getSpiritAnimalListByUid(uid)).orElse(Collections.emptyList());
        if (list.isEmpty()) {
            return null;
        }
        String uniqueKey = list.stream().filter(lnUserSpiritAnimal ->
                        WearTypeEnum.put_on.getCode().equals(lnUserSpiritAnimal.getCarryType()))
                .findFirst()
                .map(LnUserSpiritAnimal::getUniqueKey)
                .orElse(null);
        if (StringUtils.isBlank(uniqueKey)) {
            return null;
        }
        Optional<LnSpiritAnimal> spiritAnimal = Optional.ofNullable(lnButtonManager.allSpiritAnimalList().get(uniqueKey));
        return spiritAnimal.map(LnSpiritAnimal::getNrgcKey).orElse(null);
    }

    /**
     * 获取用户进房提醒特效背景
     */
    public String getUserCirKey(Long uid) {
        log.info("getUserCirKey uid: {}", uid);
        List<LnUserSpiritAnimal> list = Optional.ofNullable(lnUserSpiritAnimalService.getSpiritAnimalListByUid(uid)).orElse(Collections.emptyList());
        if (list.isEmpty()) {
            return null;
        }
        String uniqueKey = list.stream().filter(lnUserSpiritAnimal ->
                        WearTypeEnum.put_on.getCode().equals(lnUserSpiritAnimal.getCarryType()))
                .findFirst()
                .map(LnUserSpiritAnimal::getUniqueKey)
                .orElse(null);
        if (StringUtils.isBlank(uniqueKey)) {
            return null;
        }
        Optional<LnSpiritAnimal> spiritAnimal = Optional.ofNullable(lnButtonManager.allSpiritAnimalList().get(uniqueKey));
        return spiritAnimal.map(LnSpiritAnimal::getCirKey).orElse(null);
    }

    /**
     * 根据入场特效获取用户当前灵兽名称
     * @param uid
     * @param uniqueKey 入场特效唯一标识
     * @return
     */
    public String getUserSpiritAnimalName(Long uid, String uniqueKey) {
        log.info("getUserSpiritAnimalName check -uid {} -uniqueKey {}", uid, uniqueKey);
        Optional<LnSpiritAnimal> first = Optional.ofNullable(lnSpiritAnimalService.getAllForLimit(null)).orElse(Collections.emptyList())
                .stream()
                .filter(lnSpiritAnimal -> uniqueKey.equals(lnSpiritAnimal.getEseKey()))
                .findFirst();
        if (!first.isPresent()) {
            log.warn("lnSpiritAnimalService error -uniqueKey {}", uniqueKey);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        LnUserSpiritAnimal lnUserSpiritAnimal = lnUserSpiritAnimalService.getByUniqueKey(uid, first.get().getUniqueKey());
        if (Objects.isNull(lnUserSpiritAnimal)) {
            log.warn("getUserSpiritAnimalName error -uid {}", uid);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return lnUserSpiritAnimal.getNickname();
    }

    /**
     * 入场特效 - 传说中的梦幻岛活动
     */
    public List<String> checkLnEntryESE() {
        // 获取所有入场特效
        return Optional.ofNullable(lnSpiritAnimalService.getAllForLimit(null)).orElse(Collections.emptyList())
                .stream()
                .map(LnSpiritAnimal::getEseKey)
                .collect(Collectors.toList());
    }

    /**
     * 新装扮 活动（ln） 校验
     */
    public Boolean newLnDressCheck(Long uid, String type, String uniqueKey) {
        log.info("newLnDressCheck check dress up uid:{} -type {} -uniqueKey {}", uid, type, uniqueKey);
        if (Objects.isNull(uid) || StringUtils.isBlank(type) || StringUtils.isBlank(uniqueKey)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        DressUpType dressUpType = DressUpType.getByCode(type);
        if (Objects.isNull(dressUpType)) {
            // 校验装扮类型失败
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        switch (dressUpType) {
            case entry_special_effect:
                return checkEntrySpecialEffect(uid, uniqueKey);
            case red_envelope_cover:
                return checkRedEnvelopeCover(uid, uniqueKey);
            case home_cover:
                return checkHomeCover(uid, uniqueKey);
            default:
                log.warn("newLnDressCheck unknown type: {}", type);
                return Boolean.TRUE;
        }
    }

    /**
     * 入场特效校验
     */
    private Boolean checkEntrySpecialEffect(Long uid, String eseKey) {
        // 校验用户灵兽
        List<LnUserSpiritAnimal> list = lnUserSpiritAnimalService.getSpiritAnimalListByUid(uid);
        if (Objects.isNull(list) || list.isEmpty()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        // 获取所有灵兽
        HashMap<String, LnSpiritAnimal> animalHashMap = lnButtonManager.allSpiritAnimalList();

        // 不是灵兽的入场特效，直接返回
        if (!checkLnEntryESE().contains(eseKey)) {
            return Boolean.TRUE;
        }

        // 获取用户的所有入场特效
        Map<String, LnSpiritAnimal> eseKeyToSpiritAnimalMap = list.stream().map(lnUserSpiritAnimal -> {
                    LnSpiritAnimal spiritAnimal = animalHashMap.get(lnUserSpiritAnimal.getUniqueKey());
                    if (Objects.nonNull(spiritAnimal)) {
                        // 构建用户入场特效与用户的灵兽关系
                        return new AbstractMap.SimpleEntry<>(spiritAnimal.getEseKey(), spiritAnimal);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 用户携带的入场特效 - 灵兽
        LnSpiritAnimal spiritAnimal = eseKeyToSpiritAnimalMap.get(eseKey);
        if (Objects.isNull(spiritAnimal)) {
            log.warn("newDressCheck check entry special effect error: {}", eseKey);
            return Boolean.FALSE;
        }

        // 用户携带的入场特效 - 用户灵兽
        LnUserSpiritAnimal userSpiritAnimal = lnUserSpiritAnimalService.getByUniqueKey(uid, spiritAnimal.getUniqueKey());
        log.info("checkEntrySpecialEffect uid:{}, eseKey:{},  userSpiritAnimal:{}", uid, eseKey, JSON.toJSONString(userSpiritAnimal));
        // 校验灵兽是否携带
        if (Objects.isNull(userSpiritAnimal)) {
            return Boolean.FALSE;
        }
        if (WearTypeEnum.put_on.getCode().equals(userSpiritAnimal.getCarryType())) {
            return Boolean.TRUE;
        }

        // 每个灵宠每日首次携带的时候-1体力值，后续切换不扣
        String key = String.format(CARRY_NX_KEY, DateUtil.getNowYyyyMMdd(), uid, userSpiritAnimal.getUniqueKey());
        if (redisManager.hasKey(key)) {
            return Boolean.TRUE;
        }

        // 校验灵兽体力是否 > 0
        boolean zreoStamina = userSpiritAnimal.getStamina() <= 0;

        // 体力小于0 拒绝
        if (zreoStamina) {
            return Boolean.FALSE;
        }

        // 扣减体力，设置nx
        if (!lnUserSpiritAnimalService.deductStamina(uid, userSpiritAnimal.getUniqueKey(), CARRY_SUB_STAMINA)) {
            return Boolean.FALSE;
        }
        redisManager.set(key, 1, DateUtil.ONE_DAY_SECONDS);

        return Boolean.TRUE;
    }

    /**
     * 红包封面校验
     */
    private Boolean checkRedEnvelopeCover(Long uid, String recKey) {
        // 校验用户灵兽
        List<LnUserSpiritAnimal> list = lnUserSpiritAnimalService.getSpiritAnimalListByUid(uid);
        if (Objects.isNull(list) || list.isEmpty()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 获取所有灵兽
        HashMap<String, LnSpiritAnimal> animalHashMap = lnButtonManager.allSpiritAnimalList();

        // 获取用户的所有红包封面
        Map<String, LnSpiritAnimal> hcKeyToSpiritAnimalMap = list.stream().map(lnUserSpiritAnimal -> {
                    LnSpiritAnimal spiritAnimal = animalHashMap.get(lnUserSpiritAnimal.getUniqueKey());
                    if (Objects.nonNull(spiritAnimal)) {
                        // 构建用户红包封面与用户的灵兽关系
                        return new AbstractMap.SimpleEntry<>(spiritAnimal.getRecKey(), spiritAnimal);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 用户携带的红包封面 - 灵兽
        LnSpiritAnimal spiritAnimal = hcKeyToSpiritAnimalMap.get(recKey);
        if (Objects.isNull(spiritAnimal)) {
            log.warn("checkRedEnvelopeCover check entry special error: {}", recKey);
            return Boolean.FALSE;
        }
        // 用户携带的红包封面 - 用户灵兽
        LnUserSpiritAnimal userSpiritAnimal = lnUserSpiritAnimalService.getByUniqueKey(uid, spiritAnimal.getUniqueKey());
        // 校验灵兽是否携带
        if (Objects.isNull(userSpiritAnimal)) {
            return Boolean.FALSE;
        }
        log.info("checkRedEnvelopeCover uid:{}, recKey:{},  userSpiritAnimal:{}", uid, recKey, JSON.toJSONString(userSpiritAnimal));
        if (WearTypeEnum.put_on.getCode().equals(userSpiritAnimal.getCarryType())) {
            return Boolean.TRUE;
        }

        // 每个灵宠每日首次携带的时候-1体力值，后续切换不扣
        String key = String.format(CARRY_NX_KEY, DateUtil.getNowYyyyMMdd(), uid, userSpiritAnimal.getUniqueKey());
        if (redisManager.hasKey(key)) {
            return Boolean.TRUE;
        }

        // 校验灵兽体力是否 > 0
        boolean zreoStamina = userSpiritAnimal.getStamina() <= 0;

        // 体力小于0 拒绝
        if (zreoStamina) {
            return Boolean.FALSE;
        }

        // 扣减体力，设置nx
        if (!lnUserSpiritAnimalService.deductStamina(uid, userSpiritAnimal.getUniqueKey(), CARRY_SUB_STAMINA)) {
            return Boolean.FALSE;
        }
        redisManager.set(key, 1, DateUtil.ONE_DAY_SECONDS);

        return Boolean.TRUE;
    }

    /**
     * 校验主页封面
     */
    private Boolean checkHomeCover(Long uid, String hcKey) {
        // 校验用户灵兽
        List<LnUserSpiritAnimal> list = lnUserSpiritAnimalService.getSpiritAnimalListByUid(uid);
        if (Objects.isNull(list) || list.isEmpty()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        // 获取所有灵兽
        HashMap<String, LnSpiritAnimal> animalHashMap = lnButtonManager.allSpiritAnimalList();

        // 获取用户的所有主页封面
        Map<String, LnSpiritAnimal> recKeyToSpiritAnimalMap = list.stream().map(lnUserSpiritAnimal -> {
                    LnSpiritAnimal spiritAnimal = animalHashMap.get(lnUserSpiritAnimal.getUniqueKey());
                    if (Objects.nonNull(spiritAnimal)) {
                        // 构建用户主页封面与用户的灵兽关系
                        return new AbstractMap.SimpleEntry<>(spiritAnimal.getHcKey(), spiritAnimal);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 用户携带的主页封面 - 灵兽
        LnSpiritAnimal spiritAnimal = recKeyToSpiritAnimalMap.get(hcKey);
        if (Objects.isNull(spiritAnimal)) {
            log.warn("checkHomeCover check entry special error: {}", hcKey);
            return Boolean.FALSE;
        }
        // 用户携带的主页封面 - 用户灵兽
        LnUserSpiritAnimal userSpiritAnimal = lnUserSpiritAnimalService.getByUniqueKey(uid, spiritAnimal.getUniqueKey());
        log.info("checkHomeCover uid:{}, hcKey:{},  userSpiritAnimal:{}", uid, hcKey, JSON.toJSONString(userSpiritAnimal));
        // 校验灵兽是否携带
        if (Objects.isNull(userSpiritAnimal)) {
            return Boolean.FALSE;
        }
        if (WearTypeEnum.put_on.getCode().equals(userSpiritAnimal.getCarryType())) {
            return Boolean.TRUE;
        }

        // 每个灵宠每日首次携带的时候-1体力值，后续切换不扣
        String key = String.format(CARRY_NX_KEY, DateUtil.getNowYyyyMMdd(), uid, userSpiritAnimal.getUniqueKey());
        if (redisManager.hasKey(key)) {
            return Boolean.TRUE;
        }

        // 校验灵兽体力是否 > 0
        boolean zreoStamina = userSpiritAnimal.getStamina() <= 0;

        // 体力小于0 拒绝
        if (zreoStamina) {
            return Boolean.FALSE;
        }

        // 扣减体力，设置nx
        if (!lnUserSpiritAnimalService.deductStamina(uid, userSpiritAnimal.getUniqueKey(), CARRY_SUB_STAMINA)) {
            return Boolean.FALSE;
        }
        redisManager.set(key, 1, DateUtil.ONE_DAY_SECONDS);

        return Boolean.TRUE;
    }
}
