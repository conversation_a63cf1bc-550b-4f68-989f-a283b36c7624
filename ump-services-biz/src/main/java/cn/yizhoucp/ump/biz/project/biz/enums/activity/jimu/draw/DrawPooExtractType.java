package cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw;

/**
 * <AUTHOR>
 * @Description 奖池获取类型
 * @date 2022-07-10 21:03
 */
public enum DrawPooExtractType {

    pool("pool","固定奖品数量进行抽取"),
    pool_everyone_only("pool_everyone_only","固定奖品数量进行抽取，每人一个奖池"),
    random("random","通过概率进行随机"),
    ;


    private String code;

    private String desc;

    DrawPooExtractType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
