package cn.yizhoucp.ump.biz.project.biz.manager.activity.eternalLove;

import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.GiftFrom;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignRoomService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BuoyInfoHandler;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.BOX_GIFT_SET;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FAMILY_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GIRL_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GLOBAL_FLOWER_GOD_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GROWTH_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.LOTTERY_GIFT_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.RELATIONSHIP_FORM;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.Task.TASK_2;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.Task.TASK_3;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.Task.TASK_4;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.Task.TASK_5;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.Task.TASK_6;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.Task.TASK_7;

@Service
@Slf4j
public class EternalLoveBizManager implements BuoyInfoHandler {

    @Resource
    private ActivityManager activityManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private EternalLoveRankManager eternalLoveRankManager;
    @Resource
    private FeignRoomService feignRoomService;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private EternalLoveTrackManager eternalLoveTrackManager;

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public Function<HandlerContext, BuoyInfoDTO> getBuoyInfoHandler() {
        return handlerContext -> {
            if (handlerContext.getToUid() == null) {
                return BuoyInfoDTO.builder().build();
            }

            BaseParam param = BaseParam.ofMDC();
            ActivityDO activityDO = activityManager.getActivityInfo(param, this.getActivityCode());
            return BuoyInfoDTO.builder().routerUrl(this.getActivityUrl(param, env, environment, activityDO.getActivityUrl()) + "&toUid=" + handlerContext.getToUid()).build();
        };
    }

    public int stage() {
        ActivityDO activityDO = activityManager.getActivityInfo(BaseParam.builder().unionId("wGF30Qq8c3").build(), this.getActivityCode());
        LocalDateTime startTime = activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();
        log.debug("startTime {} now {}", startTime, now);
        long between = ChronoUnit.DAYS.between(startTime, now);
        if (between < 7) {
            return 1;
        } else if (between < 14) {
            return 2;
        } else if (between < 21) {
            return 3;
        } else {
            return 4;
        }
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        Long uid = param.getUid();
        Long appId = param.getAppId();
        UserVO userVO = feignUserService.getBasic(uid, appId).successData();
        int stage = this.stage();

        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            Long toUid = coinGiftGivedModel.getToUid();
            String lotteryGiftKey = coinGiftGivedModel.getLotteryGiftKey();
            String giftKey = coinGiftGivedModel.getGiftKey();
            Long productCount = coinGiftGivedModel.getProductCount();
            UserVO toUserVO = feignUserService.getBasic(toUid, appId).successData();

            // 全服花神值
            if (GiftWay.NORMAL.getCode().equals(coinGiftGivedModel.getGiftWay())) {
                if (userVO != null && toUserVO != null) {
                    if (SexType.MAN.equals(userVO.getSex())) {
                        int globalFlowerValue = Optional.ofNullable(redisManager.getInteger(GLOBAL_FLOWER_GOD_VALUE)).orElse(0);
                        EternalLoveConstant.Stage eternalLoveStage = EternalLoveConstant.Stage.getGlowbalFlowerGodPercentageByStage(stage);
                        if (eternalLoveStage != null) {
                            if (globalFlowerValue < eternalLoveStage.getGlobalFlowerGodPercentage() * GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT) {
                                redisManager.set(GLOBAL_FLOWER_GOD_VALUE, (long) Math.min(globalFlowerValue + coinGiftGivedModel.getCoin(), eternalLoveStage.getGlobalFlowerGodPercentage() * GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT), DateUtil.ONE_MONTH_SECOND);
                            }
                        }
                    }
                    if (SexType.WOMAN.equals(toUserVO.getSex())) {
                        int globalFlowerValue = Optional.ofNullable(redisManager.getInteger(GLOBAL_FLOWER_GOD_VALUE)).orElse(0);
                        EternalLoveConstant.Stage eternalLoveStage = EternalLoveConstant.Stage.getGlowbalFlowerGodPercentageByStage(stage);
                        if (eternalLoveStage != null) {
                            if (globalFlowerValue < eternalLoveStage.getGlobalFlowerGodPercentage() * GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT) {
                                redisManager.set(GLOBAL_FLOWER_GOD_VALUE, (long) Math.min(globalFlowerValue + coinGiftGivedModel.getCoin(), eternalLoveStage.getGlobalFlowerGodPercentage() * GLOBAL_FLOWER_GOD_VALUE_A_HUNDRED_PER_CENT), DateUtil.ONE_MONTH_SECOND);
                            }
                        }
                    }
                }
            }

            // 花神赐福
            String splicUid = AppUtil.splicUserId(uid, toUid);
            if (LOTTERY_GIFT_KEY.contains(lotteryGiftKey)) {
                redisManager.sSetExpire(String.format(RELATIONSHIP_FORM, uid), DateUtil.ONE_MONTH_SECOND, toUid);
                redisManager.sSetExpire(String.format(RELATIONSHIP_FORM, toUid), DateUtil.ONE_MONTH_SECOND, uid);
                redisManager.incrLong(String.format(GROWTH_VALUE, splicUid), coinGiftGivedModel.getCoin(), DateUtil.ONE_MONTH_SECOND);
            }

            // 每日花神任务
            if (userVO != null) {
                if (SexType.MAN.equals(userVO.getSex())) {
                    if ("ZHZ_GIFT".equals(lotteryGiftKey)) {
                        Long task2 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_2), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task2 >= TASK_2.getFinishTimes() && task2 - productCount < TASK_2.getFinishTimes()) {
                            eternalLoveTrackManager.allActivityTaskFinish("task9", uid, null, TASK_2.getFlowerGodToken(), null);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task3 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_3), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task3 >= TASK_3.getFinishTimes() && task3 - productCount < TASK_3.getFinishTimes()) {
                                eternalLoveTrackManager.allActivityTaskFinish("task10", uid, null, TASK_3.getFlowerGodToken(), null);
                            }
                        }
                    } else if ("CXS1_GIFT".equals(lotteryGiftKey)) {
                        Long task4 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_4), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task4 >= TASK_4.getFinishTimes() && task4 - productCount < TASK_4.getFinishTimes()) {
                            eternalLoveTrackManager.allActivityTaskFinish("task11", uid, null, TASK_4.getFlowerGodToken(), null);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task5 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_5), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task5 >= TASK_5.getFinishTimes() && task5 - productCount < TASK_5.getFinishTimes()) {
                                eternalLoveTrackManager.allActivityTaskFinish("task12", uid, null, TASK_5.getFlowerGodToken(), null);
                            }
                        }
                    } else if ("MXL_GIFT".equals(lotteryGiftKey)) {
                        Long task6 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_6), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task6 >= TASK_6.getFinishTimes() && task6 - productCount < TASK_6.getFinishTimes()) {
                            eternalLoveTrackManager.allActivityTaskFinish("task13", uid, null, TASK_6.getFlowerGodToken(), null);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task7 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_7), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task7 >= TASK_7.getFinishTimes() && task7 - productCount < TASK_7.getFinishTimes()) {
                                eternalLoveTrackManager.allActivityTaskFinish("task14", uid, null, TASK_7.getFlowerGodToken(), null);
                            }
                        }
                    }
                }
            }
            if (toUserVO != null) {
                if (SexType.WOMAN.equals(toUserVO.getSex())) {
                    if ("ZHZ_GIFT".equals(lotteryGiftKey)) {
                        Long task9 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_2), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task9 >= TASK_2.getFinishTimes() && task9 - productCount < TASK_2.getFinishTimes()) {
                            eternalLoveTrackManager.allActivityTaskFinish("task2", uid, null, TASK_2.getFlowerGodToken(), null);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task10 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_3), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task10 >= TASK_3.getFinishTimes() && task10 - productCount < TASK_3.getFinishTimes()) {
                                eternalLoveTrackManager.allActivityTaskFinish("task3", uid, null, TASK_3.getFlowerGodToken(), null);
                            }
                        }
                    } else if ("CXS1_GIFT".equals(lotteryGiftKey)) {
                        Long task11 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_4), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task11 >= TASK_4.getFinishTimes() && task11 - productCount < TASK_4.getFinishTimes()) {
                            eternalLoveTrackManager.allActivityTaskFinish("task4", uid, null, TASK_4.getFlowerGodToken(), null);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task12 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_5), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task12 >= TASK_5.getFinishTimes() && task12 - productCount < TASK_5.getFinishTimes()) {
                                eternalLoveTrackManager.allActivityTaskFinish("task5", uid, null, TASK_5.getFlowerGodToken(), null);
                            }
                        }
                    } else if ("MXL_GIFT".equals(lotteryGiftKey)) {
                        Long task13 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_6), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task13 >= TASK_6.getFinishTimes() && task13 - productCount < TASK_6.getFinishTimes()) {
                            eternalLoveTrackManager.allActivityTaskFinish("task6", uid, null, TASK_6.getFlowerGodToken(), null);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task14 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_7), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task14 >= TASK_7.getFinishTimes() && task14 - productCount < TASK_7.getFinishTimes()) {
                                eternalLoveTrackManager.allActivityTaskFinish("task7", uid, null, TASK_7.getFlowerGodToken(), null);
                            }
                        }
                    }
                }
            }

            // 花神值
            if (GiftWay.NORMAL.getCode().equals(coinGiftGivedModel.getGiftWay())) {
                if (userVO != null) {
                    if (SexType.MAN.equals(userVO.getSex())) {
                        redisManager.incrLong(String.format(FLOWER_GOD_VALUE, uid), coinGiftGivedModel.getCoin(), DateUtil.ONE_MONTH_SECOND);
                    }
                }
                if (toUserVO != null) {
                    if (SexType.WOMAN.equals(toUserVO.getSex())) {
                        redisManager.incrLong(String.format(FLOWER_GOD_VALUE, toUid), coinGiftGivedModel.getCoin(), DateUtil.ONE_MONTH_SECOND);
                    }
                }
            }

            // 女生榜单
            if (userVO != null && SexType.MAN.equals(userVO.getSex()) && toUserVO != null && SexType.WOMAN.equals(toUserVO.getSex())) {
                if (LOTTERY_GIFT_KEY.contains(lotteryGiftKey)) {
                    eternalLoveRankManager.incrRankValue(toUid, coinGiftGivedModel.getCoin(), String.format(GIRL_RANK, stage));
                }
            }

            // 累计家族榜单
            if (GiftWay.NORMAL.getCode().equals(coinGiftGivedModel.getGiftWay())) {
                Long familyIdByScene = findBelongFamilyIdByScene(param, coinGiftGivedModel.getRelationId(), coinGiftGivedModel.getFrom());
                if (Objects.nonNull(familyIdByScene)) {
                    if (stage == 1) {
                        eternalLoveRankManager.incrRankValue(familyIdByScene, coinGiftGivedModel.getCoin(), String.format(FAMILY_RANK, stage));
                    } else if (stage == 2 || stage == 3) {
                        RankVO rank = eternalLoveRankManager.getRank(RankContext.builder()
                                .rankKey(String.format(FAMILY_RANK, stage - 1))
                                .param(param)
                                .activityCode(ACTIVITY_CODE)
                                .type(RankContext.RankType.family)
                                .build());
                        List<RankItem> rankList = rank.getRankList();
//                        List<Long> familyIdList = rankList.stream().map(RankItem::getId).collect(Collectors.toList());
//                        if (familyIdList.contains(familyIdByScene)) {
                            eternalLoveRankManager.incrRankValue(familyIdByScene, coinGiftGivedModel.getCoin(), String.format(FAMILY_RANK, stage));
//                        }
                    }
                } else {
                    log.info("dragon 送礼处理 uid {} 没有加入家族", param.getUid());
                }
            }
        }

        return Boolean.TRUE;
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Boolean stayRoomDurationHandle(BaseParam param, JSONObject bizParam) {
        Integer time = bizParam.getInteger("time");
        if (time != null && time != 0) {
            UserVO userVO = feignUserService.getBasic(param.getUid(), param.getAppId()).successData();
            if (userVO != null && SexType.MAN.equals(userVO.getSex())) {
//                redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), DateUtil.getNowYyyyMMdd(), TASK_1), 1L, DateUtil.ONE_MONTH_SECOND);
            }
        }

        return Boolean.TRUE;
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Boolean setSeatDurationHandle(BaseParam param, JSONObject bizParam) {
        Integer time = bizParam.getInteger("time");
        if (time != null && time != 0) {
            UserVO userVO = feignUserService.getBasic(param.getUid(), param.getAppId()).successData();
            if (userVO != null && SexType.WOMAN.equals(userVO.getSex())) {
//                redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), DateUtil.getNowYyyyMMdd(), TASK_1), 1L, DateUtil.ONE_MONTH_SECOND);
            }
        }

        return Boolean.TRUE;
    }

    private String getActivityUrl(BaseParam param, String env, Environment environment, String activityUrl) {
        return String.format(
                ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + activityUrl + "?uid=%s&from=chat_buoy",
                param.getUid()
        );
    }

    private Long findBelongFamilyIdByScene(BaseParam param, Long relationId, String from) {
        log.debug("findBelongFamilyIdByScene param {} relationId {} from  {}", JSON.toJSONString(param), relationId, from);
        if (StringUtils.equalsIgnoreCase(from, GiftFrom.room.getCode())) {
            RoomVO roomVO = feignRoomService.getRoomInfoByRoomId(relationId, param.getAppId()).successData();
            if (Objects.isNull(roomVO)) {
                return null;
            }
            return findBelongFamilyId(BaseParam.builder().appId(roomVO.getAppId()).uid(roomVO.getRoomOwnerId()).build());
        } else if (StringUtils.equalsIgnoreCase(from, GiftFrom.family_gift.getCode())) {
            return relationId;
        } else {
            //return findBelongFamilyId(param);
            return null;
        }
    }

    private Long findBelongFamilyId(BaseParam param) {
        log.debug("findBelongFamilyId param {} ", JSON.toJSONString(param));
        FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoByUid(param.getUid(), param.getAppId()).successData();
        return Objects.isNull(familyInfoDTO) ? null : familyInfoDTO.getId();
    }

}
