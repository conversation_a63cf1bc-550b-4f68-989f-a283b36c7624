package cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.common.MolConstant.*;

@Component
public class MolRedisControls {

    @Resource
    private RedisManager redisManager;


    /**
     * 获取签数量
     */
    public Long getSignValue(Long uid, String poolCode) {
        String signKye = String.format(USER_SIGN_COUNT, uid, poolCode);
        return Optional.ofNullable(redisManager.getLong(signKye)).orElse(0L);
    }

    /**
     * 增加签
     */
    public void addSignValue(Long uid, String poolCode, Long value) {
        String signKye = String.format(USER_SIGN_COUNT, uid, poolCode);
        redisManager.incrLong(signKye, value, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 扣减签
     */
    public void deductSignValue(Long uid, String poolCode, Long value) {
        String signKye = String.format(USER_SIGN_COUNT, uid, poolCode);
        redisManager.decrLong(signKye, value, DateUtil.ONE_MONTH_SECOND);
    }


    /**
     * 获取当前解锁场景位置
     */
    public Long unlockSceneLocation(String id) {
        return Optional.ofNullable(redisManager.getLong(String.format(COIN_UNLOCK_SCENE_KEY, id))).orElse(0L);
    }

    /**
     * 更改解锁场景位置
     */
    public void changeUnlockSceneLocation(String id, Long value) {
        String signKye = String.format(COIN_UNLOCK_SCENE_KEY, id);
        redisManager.set(signKye, value, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取纪念币数量
     */
    public Long getCoinValue(String id) {
        String signKye = String.format(USER_COIN_COUNT, id);
        return Optional.ofNullable(redisManager.getLong(signKye)).orElse(0L);
    }

    /**
     * 增加纪念币
     */
    public void addCoinValue(String id, Long value) {
        String signKye = String.format(USER_COIN_COUNT, id);
        redisManager.incrLong(signKye, value, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 扣减纪念币
     */
    public void deductCoinValue(String id, Long value) {
        String signKye = String.format(USER_COIN_COUNT, id);
        redisManager.decrLong(signKye, value, DateUtil.ONE_MONTH_SECOND);
    }


    /**
     * 获取用户任务完成数量
     */
    public Long getTaskValue(Long uid, String taskKey) {
        String taskKye = String.format(USER_TASK_COUNT, DateUtil.getNowYyyyMMdd(), uid, taskKey);
        return Optional.ofNullable(redisManager.getLong(taskKye)).orElse(0L);
    }

    /**
     * 增加用户任务完成数量
     */
    public Long addTaskValue(Long uid, String taskKey, Long value) {
        String taskKye = String.format(USER_TASK_COUNT, DateUtil.getNowYyyyMMdd(), uid, taskKey);
        return redisManager.incrLong(taskKye, value, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 重制用户任务完成数量
     */
    public void resetTaskValue(Long uid, String taskKey, Long value) {
        String taskKye = String.format(USER_TASK_COUNT, DateUtil.getNowYyyyMMdd(), uid, taskKey);
        redisManager.set(taskKye, value, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取用户任务状态
     * 0 - 未解锁
     * 1 - 已解锁
     * 2 - 可领取
     * 3 - 已领取
     */
    public Integer getTaskStatus(Long uid, String taskKey) {
        String taskKye = String.format(USER_TASK_STATUS, DateUtil.getNowYyyyMMdd(), uid, taskKey);
        return Optional.ofNullable(redisManager.getInteger(taskKye)).orElse(0);
    }

    /**
     * 设置用户任务状态
     */
    public void setTaskStatus(Long uid, String taskKey, Integer status) {
        String taskKye = String.format(USER_TASK_STATUS, DateUtil.getNowYyyyMMdd(), uid, taskKey);
        redisManager.set(taskKye, status, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取
     */
    /**
     * 获取跨年倒计时进度
     */
    public Long getNewYearCountdownProgress() {
        Long aLong = Optional.ofNullable(redisManager.getLong(NEW_YEAR_COUNTDOWN_PROGRESS)).orElse(0L);
        if (aLong > 2025) {
            return 2025L;
        }
        return aLong;
    }

    /**
     * 获取用户跨年一键领取状态
     */
    public Integer getNewYearGiftStatus(Long uid) {
        String key = String.format(NEW_YEAR_GIFT_STATUS, uid);
        Integer res = Optional.ofNullable(redisManager.getInteger(key)).orElse(MolEnum.StatusEnum.NOT_FINISHED.getStatusValue());
        if ((getNewYearCountdownProgress().equals(2025L)) &&
                !MolEnum.StatusEnum.COMPLETED.getStatusValue().equals(res)) {
            setNewYearGiftStatus(uid, MolEnum.StatusEnum.REWARDED_AVAILABLE.getStatusValue());
            return MolEnum.StatusEnum.REWARDED_AVAILABLE.getStatusValue();
        }

        return res;
    }

    /**
     * 设置用户跨年一键领取状态
     */
    public void setNewYearGiftStatus(Long uid, Integer status) {
        String key = String.format(NEW_YEAR_GIFT_STATUS, uid);
        redisManager.set(key, status, DateUtil.ONE_MONTH_SECOND);
    }


}
