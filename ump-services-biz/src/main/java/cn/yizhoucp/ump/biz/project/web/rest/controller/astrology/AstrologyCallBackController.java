package cn.yizhoucp.ump.biz.project.web.rest.controller.astrology;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.astrology.AstrologyCallBackManager;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.AstrologyCallbackActivityDO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class AstrologyCallBackController {
    @Resource
    private AstrologyCallBackManager astrologyCallBackManager;

    @PostMapping("/api/admin/ump/astrology-call-back/save")
    public Result save(@RequestBody AstrologyCallbackActivityDO astrologyCallbackActivityDO) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyCallBackManager.addActivity(astrologyCallbackActivityDO));
    }

    @GetMapping("/api/admin/ump/astrology-call-back/list")
    public Result<List<AstrologyCallbackActivityDO>> list() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyCallBackManager.listActivity());
    }
}
