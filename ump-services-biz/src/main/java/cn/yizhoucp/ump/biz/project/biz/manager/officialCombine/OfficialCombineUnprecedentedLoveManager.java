package cn.yizhoucp.ump.biz.project.biz.manager.officialCombine;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.AppScene;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.order.api.client.NormalOrderFeignService;
import cn.yizhoucp.order.api.vo.OrderVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.LoginPopJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.OfficialCombineUserDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.OfficialCombineUnprecedentedLoveConstant.*;

/**
 * 官宣 旷世恋人活动
 *
 * <AUTHOR>
 * @Date 2023/4/19 12:02
 * @Version 1.0
 */
@Slf4j
@Component
public class OfficialCombineUnprecedentedLoveManager implements ActivityComponent {

    private static final String PATH = "great-love";

    @Resource
    private RedisManager redisManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private NormalOrderFeignService normalOrderFeignService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private PopManager popManager;
    @Resource
    private LoginPopJpaDAO loginPopJpaDAO;
    @Resource
    private UserCoinAccountManager userCoinAccountManager;
    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 每日对满足1314亲密度的用户进行消息推送
     *
     */
    @Async("activityHandleThreadPool")
    @ActivityCheck(activityCode = "unprecedented-love", isThrowException = false)
    public void userIntimacyChangeNotify(Long appId, String unionId, Long fromUid, Long toUid, Long oldVal, Long newVal) {
        log.debug("userIntimacyChangeNotify from {} toUid {} oldVal {} newVal {}", fromUid, toUid, oldVal, newVal);
        if (oldVal < 1314 && newVal >= 1314) {
            notifyComponent.npcNotify(fromUid,
                    String.format(LOGIN_NOTIFY_MSG, ActivityUrlUtil.getH5BaseUrl(unionId, env) + PATH));
            notifyComponent.npcNotify(toUid,
                    String.format(LOGIN_NOTIFY_MSG, ActivityUrlUtil.getH5BaseUrl(unionId, env) + PATH));
        }


    }

    /**
     * 每日对进入官宣付定金页面的用户进行消息推送
     *
     * @param param
     */
    @Async("activityHandleThreadPool")
    @ActivityCheck(activityCode = "unprecedented-love", isThrowException = false)
    public void pageInfo2Notify(BaseParam param) {
        try {
            if (Boolean.TRUE.equals(redisManager.setnx(String.format(PAGE_INFO_2_IDEMPOTENT,
                    ActivityTimeUtil.getToday(getActivityCode()), param.getUid()), System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND * 3))) {
                log.debug("pageInfo2Notify {}", param.getUid());
                notifyComponent.npcNotify(param.getUid(), String.format(PAGE_INFO_2_MSG, ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env) + PATH));
            }
        } catch (Exception e) {
            log.error("depositPageNotify error caused by {}", e);
        }
    }

    /**
     * 每日对当日支付定金的用户进行消息推送
     *
     * @param param
     */
    @Async("activityHandleThreadPool")
    @ActivityCheck(activityCode = "unprecedented-love", isThrowException = false)
    public void payDepositNotify(BaseParam param) {
        try {
            if (Boolean.TRUE.equals(redisManager.setnx(String.format(PAY_DEPOSIT_IDEMPOTENT, ActivityTimeUtil.getToday(getActivityCode()), param.getUid()), System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND * 3))) {
                notifyComponent.npcNotify(param.getUid(), String.format(PAY_DEPOSIT_MSG, ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env)+ PATH));
            }
        } catch (Exception e) {
            log.error("payDepositNotify error caused by {}", e);
        }
    }

    /**
     * 每日对当日支付定金的用户进行消息推送
     *
     * @param param
     */
    @Async("activityHandleThreadPool")
    @ActivityCheck(activityCode = "unprecedented-love", isThrowException = false)
    public void chooseOfficialCombineNotify(BaseParam param, Long toUid) {
        if (Objects.isNull(toUid)) {
            return;
        }
        try {
            if (Boolean.TRUE.equals(redisManager.setnx(String.format(CHOOSE_OFFICIAL_COMBINE_IDEMPOTENT, ActivityTimeUtil.getToday(getActivityCode()), toUid), System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND * 3))) {
                notifyComponent.npcNotify(toUid, String.format(CHOOSE_OFFICIAL_COMBINE_MSG, ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env)+ PATH));
            }
        } catch (Exception e) {
            log.error("invitedOfficialCombineNotify error caused by {}", e);
        }
    }

    @Async("activityHandleThreadPool")
    @ActivityCheck(activityCode = "unprecedented-love", isThrowException = false)
    public void completeOfficialCombine(OfficialCombineUserDO officialCombineUserDO) {
        log.info("completeOfficialCombine preOrderId {} totalOrderId {}", officialCombineUserDO.getPreOrderId(), officialCombineUserDO.getOrderId());
        // 1、双方下发头像框
        sendHeadFrame(officialCombineUserDO.getAppId(), officialCombineUserDO.getUnionId(), officialCombineUserDO.getFemaleUid(), officialCombineUserDO.getMaleUid());

        OrderVO orderVO = normalOrderFeignService.getOrder(officialCombineUserDO.getOrderId()).successData();

        if (Objects.isNull(orderVO)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }

        // 2、处理付尾款用户奖励
        sendPrizeToOwner(officialCombineUserDO.getAppId(), officialCombineUserDO.getUnionId(), officialCombineUserDO.getOwnerId(), orderVO);
        // 3、处理被告白用户奖励
        sendPrizeToNonOwner(officialCombineUserDO.getAppId(), officialCombineUserDO.getUnionId(), getNonOwnerId(officialCombineUserDO), orderVO);

    }

    public JSONObject drawTest(Long coin) {
        JSONObject result = new JSONObject();
        if (Objects.isNull(coin)) {
            return result;
        }

        log.debug("test coin {}", coin);
        // 下发红包雨
        List<DrawPoolItemDO> poolItemDOS = drawPoolItemService.getByPoolCode(getCoinPoolCode(coin));
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(poolItemDOS, 1, Boolean.TRUE);
        if (CollectionUtils.isEmpty(drawPoolItems)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        result.put("红包雨", drawPoolItems.get(0).getDrawPoolItemDO().getItemKey());
        // 礼物
        List<DrawPoolItemDO> drawPoolItemDOS = drawPoolItemService.getByPoolCode(getGiftPoolCode(coin));
        if (CollectionUtils.isEmpty(drawPoolItemDOS)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        List<DrawPoolItemDTO> drawPoolItemsGift = probStrategy.getDrawPoolItems(drawPoolItemDOS, 1, Boolean.TRUE);
        if (CollectionUtils.isEmpty(drawPoolItems)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        result.put("礼物", drawPoolItemsGift.get(0).getDrawPoolItemDO().getItemName());
        return result;
    }

    private void sendPrizeToOwner(Long appId, String unionId, Long ownerId, OrderVO orderVO) {
        Long coin = orderVO.getOrderAmountVO().getCoinCount();
        String coinPoolCode = getCoinPoolCode(coin);
        log.debug("sendPrizeToOwner coinPoolCode {}", coinPoolCode);

        // 下发红包雨
        List<DrawPoolItemDO> poolItemDOS = drawPoolItemService.getByPoolCode(coinPoolCode);
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(poolItemDOS, 1, Boolean.TRUE);
        if (CollectionUtils.isEmpty(drawPoolItems)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        DrawPoolItemDTO drawPoolItemDTO = drawPoolItems.get(0);
        log.info("sendPrizeToOwner spentCoin {} prize {} uid {}", coin, JSON.toJSONString(drawPoolItemDTO), ownerId);
        //sendPrizeManager.sendPrize(BaseParam.builder().appId(appId).unionId(unionId).uid(ownerId).build(), Lists.newArrayList(SendPrizeDTO.of(drawPoolItemDTO)));
        boolean sendCoinResult = userCoinAccountManager.sendCoin(unionId, appId, ownerId, null, AppScene.activity, drawPoolItemDTO.getDrawPoolItemDO().getScene(), 0L,
                Long.valueOf(drawPoolItemDTO.getDrawPoolItemDO().getItemKey()), drawPoolItemDTO.getDrawPoolItemDO().getScene(), drawPoolItemDTO.getDrawPoolItemDO().getScene(), "红包雨", null, 1L);
        log.info("sendPrizeToOwner coin {} uid {} result {}", drawPoolItemDTO.getDrawPoolItemDO().getItemKey(), ownerId, sendCoinResult);
        // 下发戒指
        DrawPoolItemDTO ring = getRingByCoin(coin);
        if (Objects.isNull(ring)) {
            log.error("不符合条件,不下发戒指 coin {}", coin);
            return;
        }
        log.info("sendPrizeToOwner uid {} coin {} ring {}", ownerId, coin, JSON.toJSONString(ring));
        sendPrizeManager.sendPrize(BaseParam.builder().unionId(unionId).appId(appId).uid(ownerId).build(), Lists.newArrayList(SendPrizeDTO.of(ring)));
        // 推送弹窗
        doPop(appId, unionId, ownerId, ring.getDrawPoolItemDO().getItemKey());
    }

    private void doPop(Long appId, String unionId, Long uid, String val) {
        log.debug("doPop uid {}  val {}", uid, val);
        Optional<LoginPopDO> loginPopJpaDAOOne = loginPopJpaDAO.findOne(Example.of(LoginPopDO.builder().code("unprecedented-love").bizKey("unprecedented-love-ring").build()));
        if (loginPopJpaDAOOne.isPresent()) {
            LoginPopDO popDO = loginPopJpaDAOOne.get();
            //popDO.setActionUrl(String.format(popDO.getActionUrl(), val));
            popDO.setUrl(String.format(popDO.getUrl(), val));
            log.debug("doPop actionUrl {}", popDO.getActionUrl());
            popManager.popByLoginPopDO(appId, unionId, uid, popDO);
        }
    }


    private void sendPrizeToNonOwner(Long appId, String unionId, Long nonOwner, OrderVO orderVO) {
        Long coin = orderVO.getOrderAmountVO().getCoinCount();
        List<DrawPoolItemDO> drawPoolItemDOS = drawPoolItemService.getByPoolCode(getGiftPoolCode(coin));
        if (CollectionUtils.isEmpty(drawPoolItemDOS)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(drawPoolItemDOS, 1, Boolean.TRUE);
        if (CollectionUtils.isEmpty(drawPoolItems)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        log.info("sendPrizeToNonOwner nonOwner {} prize {}", nonOwner, JSON.toJSONString(drawPoolItems.get(0)));
        sendPrizeManager.sendPrize(BaseParam.builder().appId(appId).unionId(unionId).uid(nonOwner).build(), Lists.newArrayList(SendPrizeDTO.of(drawPoolItems.get(0))));
        doPop(appId, unionId, nonOwner, drawPoolItems.get(0).getDrawPoolItemDO().getItemKey());
    }

    private void sendHeadFrame(Long appId, String unionId, Long uid1, Long uid2) {
        ArrayList<SendPrizeDTO> sendPrizeDTOS = Lists.newArrayList(SendPrizeDTO.of(DrawPoolItemDTO.builder().drawPoolItemDO(DrawPoolItemDO.builder()
                .itemName("甜蜜情侣头像框")
                .itemType("dress_up")
                .itemSubType("head_frame")
                .itemKey("tianmiqinglv_head_frame")
                .itemNum(1)
                .itemEffectiveDay(7)
                .itemValueGold(1314L)
                .extData("{\"scene\":\"unprecedented-love\"}")
                .build()).targetTimes(1).build()));
        log.info("sendHeadFrame uid1 {} uid2 {} prize {}", uid1, uid2, JSON.toJSON(sendPrizeDTOS));
        sendPrizeManager.sendPrize(BaseParam.builder().appId(appId).unionId(unionId).uid(uid1).build(), sendPrizeDTOS);
        sendPrizeManager.sendPrize(BaseParam.builder().appId(appId).unionId(unionId).uid(uid2).build(), sendPrizeDTOS);
    }

    private Long getNonOwnerId(OfficialCombineUserDO officialCombineUserDO) {
        if (Objects.isNull(officialCombineUserDO) || Objects.isNull(officialCombineUserDO.getOwnerId()) ||
                Objects.isNull(officialCombineUserDO.getFemaleUid()) || Objects.isNull(officialCombineUserDO.getMaleUid())) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        Long ownerId = officialCombineUserDO.getOwnerId();
        if (ownerId.equals(officialCombineUserDO.getMaleUid())) {
            return officialCombineUserDO.getFemaleUid();
        } else {
            return officialCombineUserDO.getMaleUid();
        }
    }


    private String getCoinPoolCode(Long coin) {
        if (coin < 39999L) {
            return "lover_coin_less_39999";
        } else if (coin >= 39999L && coin < 89999L) {
            return "lover_coin_39999_89999";
        } else if (coin >= 89999L && coin < 131400L) {
            return "lover_coin_89999_131400";
        } else if (coin >= 131400L && coin < 219999L) {
            return "lover_coin_131400_219999";
        } else {
            return "lover_coin_more_219999";
        }
    }

    private String getGiftPoolCode(Long coin) {
        if (coin <= 39999L) {
            return "lover_gift_less_39999";
        } else if (coin > 39999L && coin <= 89999L) {
            return "lover_gift_39999_89999";
        } else if (coin > 89999L && coin <= 131400L) {
            return "lover_gift_89999_131400";
        } else if (coin > 131400L && coin <= 219999L) {
            return "lover_gift_131400_219999";
        } else {
            return "lover_gift_more_219999";
        }
    }

    private DrawPoolItemDTO getRingByCoin(Long coin) {
        if (coin > 39999L && coin <= 89999L) {
            return DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(DrawPoolItemDO.builder()
                            .itemName("鲜花戒指")
                            .itemType("gift")
                            .itemKey("XHJZ_GIFT")
                            .itemNum(1)
                            .itemEffectiveDay(15)
                            .itemValueGold(5200L)
                            .extData("{\"scene\":\"unprecedented-love\"}")
                            .build()).build();
        } else if (coin > 89999L && coin <= 131400L) {
            return DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(DrawPoolItemDO.builder()
                            .itemName("星空戒指")
                            .itemType("gift")
                            .itemKey("XKJZ_GIFT")
                            .itemNum(1)
                            .itemEffectiveDay(15)
                            .itemValueGold(13140L)
                            .extData("{\"scene\":\"unprecedented-love\"}")
                            .build()).build();
        } else if (coin > 131400L && coin <= 219999L) {
            return DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(DrawPoolItemDO.builder()
                            .itemName("旷世爱恋戒指")
                            .itemType("gift")
                            .itemKey("KSALJZ_GIFT")
                            .itemNum(1)
                            .itemEffectiveDay(15)
                            .itemValueGold(19999L)
                            .extData("{\"scene\":\"unprecedented-love\"}")
                            .build()).build();
        } else if (coin > 219999L) {
            return DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(DrawPoolItemDO.builder()
                            .itemName("星河挚爱戒指")
                            .itemType("gift")
                            .itemKey("XHZAJZ_GIFT")
                            .itemNum(1)
                            .itemEffectiveDay(15)
                            .itemValueGold(52000L)
                            .extData("{\"scene\":\"unprecedented-love\"}")
                            .build()).build();
        } else {
            return null;
        }
    }


    /**
     * 获取活动 code
     *
     * @return
     */
    @Override
    public String getActivityCode() {
        return ActivityCheckListEnum.UNPRECEDENTED_LOVE.getCode();
    }

}
