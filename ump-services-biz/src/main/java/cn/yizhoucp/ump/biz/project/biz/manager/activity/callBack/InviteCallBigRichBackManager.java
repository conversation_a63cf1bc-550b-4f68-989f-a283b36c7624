package cn.yizhoucp.ump.biz.project.biz.manager.activity.callBack;

import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ump.biz.project.biz.constant.RedisConstant;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.callback.InviteTaskStatusEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AbstractManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.UserActivityManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.InviteCallbackRegisterTaskJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.InviteCallbackRegisterTaskJpaDetailDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.InviteCallbackRegisterTaskDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.InviteCallbackRegisterTaskDetailDO;
import cn.yizhoucp.ms.core.base.enums.PointBusinessType;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.api.vo.callback.InviteIndexVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 邀请大户唤回活动
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class InviteCallBigRichBackManager extends AbstractManager {

    @Resource
    InviteCallbackRegisterTaskJpaDAO registerTaskJpaDao;

    @Resource
    InviteCallbackRegisterTaskJpaDetailDAO registerTaskDetailJpaDao;

    @Resource
    UserActivityManager userActivityManager;

    @Resource
    RedisManager redisManager;

    /** 完成邀请任务数 */
    private static final Integer FINISH_INVITE_TASK_NUM = 3;

    /** 任务完成下发积分 10 * 100 * 10 */
    private static final Long FINISH_INVITE_TASK_PRIZE = 10000L;

    /** 充值和提现任务下发积分 40 * 100 * 10 */
    private static final Long FINISH_RECHARGE_AND_WITHDRAW_TASK_PRIZE = 40000L;

    /** 充值和提现任务邀请 单位：分 */
    private static final Long FINISH_RECHARGE_AND_WITHDRAW_TASK_AMOUNT = 100000L;

    /** 完成邀请 3 人任务 memo */
    private static final String FINISH_INVITE_TASK_MSG = "完成邀请3人任务奖励";

    /** 被邀请人累计充值/提现1000元奖励 memo */
    private static final String FINISH_INVITE_RECHARGE_WITHDRAW_TASK_MSG = "被邀请人累计充值/提现1000元奖励";

    /** 埋点事件名 */
    private static final String INVITE_ACTIVITY_REWARD = "recall_Invitation_activity_reward";

    private static final String REGISTER = "register";
    private static final String REWARD = "reward";

    /**
     * 邀请唤回首页
     *
     * @param uid 用户id
     * @return InviteIndexVO
     */
    public InviteIndexVO getInviteIndex(Long uid) {
        InviteCallbackRegisterTaskDO taskInfo = registerTaskJpaDao.findByUidAndActivityCode(uid, ActivityCheckListEnum.BIG_R_CALL_BACK.getCode());
        if (Objects.isNull(taskInfo)) {
            return InviteIndexVO.builder()
                    .completeNum(FINISH_INVITE_TASK_NUM)
                    .inviteNum(0)
                    .completeAmount(FINISH_RECHARGE_AND_WITHDRAW_TASK_AMOUNT/100)
                    .curAmount(0L)
                    .build();
        }
        return InviteIndexVO.builder()
                .completeNum(FINISH_INVITE_TASK_NUM)
                .inviteNum(taskInfo.getInviteNum())
                .completeAmount(FINISH_RECHARGE_AND_WITHDRAW_TASK_AMOUNT/100)
                .curAmount(taskInfo.getAmount()/100)
                .build();
    }

    /**
     * 邀请用户完成注册
     *
     * @param unionId 应用唯一标识
     * @param uid     用户id
     * @return Boolean
     */
    public Boolean inviteUserRegister(String unionId, Long appId, Long uid, Long invitedUid) {
        log.info("unionId {} appId {} uid {} invitedUid {}", unionId, appId, uid, invitedUid);
        // 判断用户是否能参与活动
        Boolean activityOpen = userActivityManager.activeCondition(appId, unionId, uid, ActivityCheckListEnum.BIG_R_CALL_BACK);
        log.info("activityOpen {}", activityOpen);
        if (!activityOpen) {
            return false;
        }
        // 任务超时的用户缓存
        String expireKey = String.format(RedisConstant.INVITE_REGISTER_TASK_EXPIRE, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE));
        if (redisManager.setIsMember(expireKey, uid)) {
            return false;
        }
        // 获取任务进度
        InviteCallbackRegisterTaskDO taskInfo = registerTaskJpaDao.findByUidAndActivityCode(uid, ActivityCheckListEnum.BIG_R_CALL_BACK.getCode());
        if (Objects.isNull(taskInfo)) {
            taskInfo = this.defaultTaskDetail(unionId, appId, uid);
        }
        List<Long> invitedUidList = JSONObject.parseArray(taskInfo.getInviteJson(), Long.class);
        // 任务校验
        if (!this.taskCheck(taskInfo, invitedUidList, invitedUid, expireKey)) {
            return false;
        }
        invitedUidList.add(invitedUid);
        taskInfo.setInviteJson(JSON.toJSONString(invitedUidList));
        taskInfo.setInviteNum(taskInfo.getInviteNum() + 1);
        taskInfo.setUpdateTime(LocalDateTime.now());
        if (taskInfo.getInviteNum().equals(FINISH_INVITE_TASK_NUM)) {
            if (taskInfo.getAmount() >= FINISH_RECHARGE_AND_WITHDRAW_TASK_AMOUNT) {
                taskInfo.setStatus(InviteTaskStatusEnum.finish.getStatus());
            }
            // 下发奖励
            this.sendPrize(appId, uid, ActivityCheckListEnum.BIG_R_CALL_BACK.getCode(), FINISH_INVITE_TASK_PRIZE, FINISH_INVITE_TASK_MSG);
            this.inviteCallbackTrack(appId, uid, REGISTER);
        }
        taskInfo = registerTaskJpaDao.save(taskInfo);
        // 保存明细
        this.saveRegisterDetail(taskInfo.getId(), uid, invitedUid);
        return true;
    }

    /**
     * 任务校验
     *
     * @param taskInfo       任务信息
     * @param invitedUidList 历史被邀请用户
     * @param invitedUid     当前被邀请用户
     * @param expireKey      完成key
     * @return boolean
     */
    private boolean taskCheck(InviteCallbackRegisterTaskDO taskInfo, List<Long> invitedUidList, Long invitedUid, String expireKey) {
        if (InviteTaskStatusEnum.overtime.getStatus() == taskInfo.getStatus()) {
            // 过期任务不处理
            redisManager.sSetExpire(expireKey, DateUtil.ONE_DAY_SECOND, taskInfo.getUid());
            return false;
        }
        if (System.currentTimeMillis() > taskInfo.getExpireTime()) {
            // 任务过期不处理
            taskInfo.setStatus(InviteTaskStatusEnum.overtime.getStatus());
            taskInfo.setUpdateTime(LocalDateTime.now());
            redisManager.sSetExpire(expireKey, DateUtil.ONE_DAY_SECOND, taskInfo.getUid());
            registerTaskJpaDao.save(taskInfo);
            return false;
        }
        // 邀请过的用户不处理
        return !invitedUidList.contains(invitedUid);
    }

    /**
     * 下发积分奖励
     *
     * @param appId 应用id
     * @param uid   用户id
     */
    private void sendPrize(Long appId, Long uid, String activityCode, Long point, String memo) {
        // 下发具体奖励
        boolean result = sendPrizeComponent.sendFreePoint(appId, uid, activityCode, point, ActivityCheckListEnum.BIG_R_CALL_BACK.getCode(),
                PointBusinessType.INVITE_PRIZE.getCode(), memo);
        if (!result) {
            log.error("下发积分奖励失败 uid {} point {}", uid, point);
        }
    }

    /**
     * 初始任务
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @return InviteCallbackRegisterTaskDetailDO
     */
    private InviteCallbackRegisterTaskDO defaultTaskDetail(String unionId, Long appId, Long uid) {
        InviteCallbackRegisterTaskDO taskDetail = new InviteCallbackRegisterTaskDO();
        taskDetail.setUnionId(unionId);
        taskDetail.setAppId(appId);
        taskDetail.setUid(uid);
        taskDetail.setActivityCode(ActivityCheckListEnum.BIG_R_CALL_BACK.getCode());
        taskDetail.setExpireTime(DateUtil.getNDaysBefore(-7).getTime());
        taskDetail.setInviteJson(new JSONArray().toJSONString());
        taskDetail.setInviteNum(0);
        taskDetail.setAmount(0L);
        taskDetail.setStatus(InviteTaskStatusEnum.progressing.getStatus());
        taskDetail.setCreateTime(LocalDateTime.now());
        return taskDetail;
    }

    /**
     * 保存邀请用户注册记录
     *
     * @param taskId     任务id
     * @param uid        用户id
     * @param invitedUid 被邀请用户id
     */
    private void saveRegisterDetail(Long taskId, Long uid, Long invitedUid) {
        InviteCallbackRegisterTaskDetailDO taskDetail = new InviteCallbackRegisterTaskDetailDO();
        taskDetail.setTaskId(taskId);
        taskDetail.setUid(uid);
        taskDetail.setInvitedUid(invitedUid);
        taskDetail.setCreateTime(LocalDateTime.now());
        taskDetail.setUpdateTime(LocalDateTime.now());
        registerTaskDetailJpaDao.save(taskDetail);
    }

    /**
     * 用户充值或提现
     *
     * @param appId  应用id
     * @param uid    用户id
     * @param amount 充值/提现金额 (分)
     * @return boolean
     */
    public boolean invitedUserRechargeOrWithdraw(Long appId, Long uid, Long amount) {
        // 非被邀请注册用户（或者任务已过期）不处理
        String notInvitedKey = String.format(RedisConstant.NOT_INVITED_REGISTER_CACHE, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE));
        if (redisManager.setIsMember(notInvitedKey, uid)) {
            return false;
        }
        InviteCallbackRegisterTaskDetailDO registerDetail = registerTaskDetailJpaDao.findByInvitedUid(uid);
        if (Objects.isNull(registerDetail)) {
            redisManager.sSetExpire(notInvitedKey, DateUtil.ONE_DAY_SECOND, uid);
            return false;
        }
        // 邀请用户
        Long inviteUid = registerDetail.getUid();
        // 任务超时的用户缓存
        String expireKey = String.format(RedisConstant.INVITE_REGISTER_TASK_EXPIRE, DateUtil.format(new Date(), DateUtil.YMD_WITHOUT_LINE));
        if (redisManager.setIsMember(expireKey, inviteUid)) {
            redisManager.sSetExpire(notInvitedKey, DateUtil.ONE_DAY_SECOND, uid);
            return false;
        }
        // 获取邀请用户任务进度
        InviteCallbackRegisterTaskDO taskInfo = registerTaskJpaDao.findByUidAndActivityCode(inviteUid, ActivityCheckListEnum.BIG_R_CALL_BACK.getCode());
        if (Objects.isNull(taskInfo) || InviteTaskStatusEnum.overtime.getStatus() == taskInfo.getStatus() || taskInfo.getAmount() >= FINISH_RECHARGE_AND_WITHDRAW_TASK_AMOUNT) {
            redisManager.sSetExpire(notInvitedKey, DateUtil.ONE_DAY_SECOND, uid);
            return false;
        }
        // 任务已过期
        if (System.currentTimeMillis() > taskInfo.getExpireTime()) {
            taskInfo.setStatus(InviteTaskStatusEnum.overtime.getStatus());
            taskInfo.setUpdateTime(LocalDateTime.now());
            redisManager.sSetExpire(expireKey, DateUtil.ONE_DAY_SECOND, taskInfo.getUid());
            registerTaskJpaDao.save(taskInfo);
            redisManager.sSetExpire(notInvitedKey, DateUtil.ONE_DAY_SECOND, uid);
            return false;
        }
        taskInfo.setAmount(taskInfo.getAmount() + amount);
        if (taskInfo.getAmount() >= FINISH_RECHARGE_AND_WITHDRAW_TASK_AMOUNT) {
            if (taskInfo.getInviteNum() >= FINISH_INVITE_TASK_NUM) {
                taskInfo.setStatus(InviteTaskStatusEnum.finish.getStatus());
            }
            // 下发奖励
            this.sendPrize(appId, inviteUid, ActivityCheckListEnum.BIG_R_CALL_BACK.getCode(), FINISH_RECHARGE_AND_WITHDRAW_TASK_PRIZE, FINISH_INVITE_RECHARGE_WITHDRAW_TASK_MSG);
            this.inviteCallbackTrack(appId, uid, REWARD);
        }
        taskInfo.setUpdateTime(LocalDateTime.now());
        registerTaskJpaDao.save(taskInfo);
        return true;
    }

    /**
     * 埋点
     *
     * @param appId     应用id
     * @param uid       用户id
     * @param type      类型
     */
    private void inviteCallbackTrack(Long appId, Long uid, String type) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        yzKafkaProducerManager.dataRangerTrack(appId, uid, INVITE_ACTIVITY_REWARD, params, ServicesNameEnum.ump_services.getCode());
    }

}
