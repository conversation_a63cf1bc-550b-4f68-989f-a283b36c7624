package cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonUserVO;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.activity.hallowmasDayHD.UserItemVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD.HallowmasDayHDConstant.*;

/**
 * 2024万圣节榜单 & 自定义接口实现方法
 */
@Service
@Slf4j
public class HallowmasDayHDRankManager extends AbstractRankManager {

    @Resource
    private HallowmasDayHDConstant hallowmasDayHDConstant;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Lazy
    @Resource
    private FeignLanlingService feignLanlingService;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private HallowmasDayHDTrackManager trackManager;


    /**
     * 后置处理
     */
    @Override
    protected void postProcess(RankContext rankContext) {

    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setSupportDiff(Boolean.TRUE);
    }

    /**
     * 任务领取奖励
     */
    public Boolean receiveDrawCountReward(String activityCode, String taskHDKey) {
        // 校验活动
        if (!activityCode.equals(ACTIVITY_CODE)) {
            log.warn("clockIn ByDate error activityCode:{}", activityCode);
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        // 获取当前用户
        Long uid = MDCUtil.getCurUserIdByMdc();
        if (Objects.isNull(uid)) {
            log.warn("MDCUtil getCurUserIdByMdc is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM, "MDCUtil getCurUserIdByMdc is null");
        }

        log.info("receiveDrawCountReward activityCode:{}, taskHDKey:{} -> uid:{}", activityCode, taskHDKey, uid);
        if ("taskHD1".equals(taskHDKey)) {
            if (hallowmasDayHDConstant.isTaskFinished("taskHD1", uid) && !hallowmasDayHDConstant.isRestrictedRewardTask("taskHD1", uid)) {
                // 发放头像框
                sendPrizeManager.sendDressUp(ServicesAppIdEnum.lanling.getAppId(), uid, DressUpType.head_frame, "daodangui_head_frame", 15, 1);
                hallowmasDayHDConstant.restrictRewardTask("taskHD1", uid);
                return Boolean.TRUE;
            }
        }

        if ("taskHD5".equals(taskHDKey)) {
            if (hallowmasDayHDConstant.isTaskFinished("taskHD5", uid) && !hallowmasDayHDConstant.isRestrictedRewardTask("taskHD5", uid)) {
                redisManager.incrLong(String.format(USER_CANDY_COUNT_KEY, uid), 38L, DateUtil.ONE_MONTH_SECOND);
                hallowmasDayHDConstant.restrictRewardTask("taskHD5", uid);
                return Boolean.TRUE;
            }
        }

        if ("taskHD6".equals(taskHDKey)) {
            if (hallowmasDayHDConstant.isTaskFinished("taskHD6", uid) && !hallowmasDayHDConstant.isRestrictedRewardTask("taskHD6", uid)) {
                redisManager.incrLong(String.format(USER_CANDY_COUNT_KEY, uid), 66L, DateUtil.ONE_MONTH_SECOND);
                hallowmasDayHDConstant.restrictRewardTask("taskHD6", uid);
                return Boolean.TRUE;
            }
        }

        if ("taskHD7".equals(taskHDKey)) {
            if (hallowmasDayHDConstant.isTaskFinished("taskHD7", uid) && !hallowmasDayHDConstant.isRestrictedRewardTask("taskHD7", uid)) {
                redisManager.incrLong(String.format(USER_CANDY_COUNT_KEY, uid), 45L, DateUtil.ONE_MONTH_SECOND);
                hallowmasDayHDConstant.restrictRewardTask("taskHD7", uid);
                return Boolean.TRUE;
            }
        }

        CandyGiftEnum candyGiftEnum = CandyGiftEnum.getByTask(taskHDKey);
        if (Objects.nonNull(candyGiftEnum)) {
            if (hallowmasDayHDConstant.isTaskFinished(candyGiftEnum.getTaskKey(), uid)) {
                redisManager.incrLong(String.format(USER_CANDY_COUNT_KEY, uid), candyGiftEnum.getAwardCount(), DateUtil.ONE_MONTH_SECOND);
                hallowmasDayHDConstant.rewardTask(candyGiftEnum.getTaskKey(), uid);
                redisManager.delete(String.format(USER_TASK_GIFT_COUNT_KEY, uid, candyGiftEnum.getCodeKey()));
                return Boolean.TRUE;
            } else {
                log.warn("user fetch exception:{}, taskHDKey:{}", uid, taskHDKey);
            }
        } else {
            log.warn("receiveDrawCountReward candyGiftEnum is null taskHDKey:{}", taskHDKey);
        }

        return Boolean.FALSE;

    }

    /**
     * 结伴
     */
    public Boolean companion(Long toUid) {
        SecurityUser user = SecurityUtils.getCurrentUser();
        Long userId = user.getUserId();
        if (Objects.isNull(user) || Objects.isNull(userId)) {
            log.warn("SecurityUtils getCurrentUser is null");
            return Boolean.FALSE;
        }
        if (hallowmasDayHDConstant.getPairRetain(userId, toUid) != -1L) {
            // 两个用户直接已经结伴
            log.warn("Two users have been paired directly uid:{}, toUid:{}", userId, toUid);
            return Boolean.FALSE;
        }
        UserVO userVO = feignUserService.getBasic(userId, ServicesAppIdEnum.lanling.getAppId()).getData();
        UserVO toUserVO = feignUserService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO) || Objects.isNull(toUserVO)) {
            log.warn("userVO or toUserVO is null uid:{}, toUid:{}", userId, toUid);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "只能选择异性好友哦～");
        }
        if (userVO.getSex().equals(toUserVO.getSex())) {
            log.warn("The two users are the same sex uid:{}, toUid:{}", userId, toUid);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "只能选择异性好友哦～");
        }
        // 初始化欢呼值
//        hallowmasDayHDConstant.addPairRetain(userId, toUid, 0L);
        String splicId = AppUtil.splicUserId(userId, toUid);
        String rankKey = String.format(USER_OTHER_RANK_KEY, userId);
        String toUidRankKey = String.format(USER_OTHER_RANK_KEY, toUid);
        incrRankValue(splicId, 0L, rankKey);
        incrRankValue(splicId, 0L, toUidRankKey);

        // 结对留存 处理好友列表
        String relationKeyUid = String.format(PAIR_RETAIN_KEY, userId);
        String relationKeyToUid = String.format(PAIR_RETAIN_KEY, toUid);
        redisManager.hset(relationKeyUid, toUid.toString(), 0L);
        redisManager.hset(relationKeyToUid, userId.toString(), 0L);
        return Boolean.TRUE;
    }

    /**
     * 初始化结伴
     *
     * @param uid
     * @param toUid
     */
    public void companion(Long uid, Long toUid) {
        if (Objects.isNull(uid) || Objects.isNull(toUid)) {
            log.warn("SecurityUtils getCurrentUser is null");
            return;
        }

        if (hallowmasDayHDConstant.getPairRetain(uid, toUid) != -1L) {
            // 两个用户直接已经结伴
            log.info("Two users have been paired directly uid:{}, toUid:{}", uid, toUid);
            return;
        }
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).getData();
        UserVO toUserVO = feignUserService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO) || Objects.isNull(toUserVO)) {
            log.warn("userVO or toUserVO is null uid:{}, toUid:{}", uid, toUid);
            return;
        }
        if (userVO.getSex().equals(toUserVO.getSex())) {
            log.warn("The two users are the same sex uid:{}, toUid:{}", uid, toUid);
            return;
        }
        // 初始化欢呼值
        String splicId = AppUtil.splicUserId(uid, toUid);
        String rankKey = String.format(USER_OTHER_RANK_KEY, uid);
        String toUidRankKey = String.format(USER_OTHER_RANK_KEY, toUid);
        incrRankValue(splicId, 0L, rankKey);
        incrRankValue(splicId, 0L, toUidRankKey);

        // 结对留存 处理好友列表
        String relationKeyUid = String.format(PAIR_RETAIN_KEY, uid);
        String relationKeyToUid = String.format(PAIR_RETAIN_KEY, toUid);
        redisManager.hset(relationKeyUid, toUid.toString(), 0L);
        redisManager.hset(relationKeyToUid, uid.toString(), 0L);
    }


    /**
     * 获取好友列表
     */
    public List<UserItemVO> getFriendList() {
        Long start = 0L;
        Integer num = 100;
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
        if (Objects.isNull(friendList)) {
            return null;
        }
        List<WrapUserVO> list = new ArrayList<>();
        list.addAll(friendList.getList());
        log.info("获取好友列表：{}", list);

        return list.stream().map(wrapUserVO -> {
                    CommonUserVO userVO = wrapUserVO.getUser();
                    String relationKey = String.format(PAIR_RETAIN_KEY, userVO.getId());
                    Object paired = redisManager.hget(relationKey, userVO.getId().toString());
                    Integer status = ObjectUtil.isNotNull(paired) ? 1 : 0;

                    return UserItemVO.builder()
                            .userId(userVO.getId())
                            .userName(userVO.getName())
                            .icon(userVO.getAvatar())
                            .sex(userVO.getSex())
                            .status(status)
                            .build();
                })
                .limit(50)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 获取搭档列表
     */
    public List<UserItemVO> getIntimateList() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            return new ArrayList<>();
        }
        Long userId = user.getUserId();
        String relationKey = String.format(PAIR_RETAIN_KEY, userId);
        if (!redisManager.hasKey(relationKey)) {
            log.warn("redis relationKey is null uid:{}, relationKey:{}", userId, relationKey);
            return new ArrayList<>();
        }

        String rankKey = String.format(USER_OTHER_RANK_KEY, userId);
        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(rankKey)
                .rankLen(50L)
                .type(RankContext.RankType.cp)
                .build());
        log.info("get IntimateList rankVO {}", rankVO);
        if (rankVO == null) {
            return new ArrayList<>();
        }

        List<CpRankItem> rankList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(rankList)) {
            return new ArrayList<>();
        }

        UserVO userVO = feignUserService.getBasic(userId, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO)) {
            log.warn("userVO is null uid:{}", userId);
            return new ArrayList<>();
        }
        boolean forMan = userVO.getSex().equals(SexType.MAN);
        if (forMan) {
            return rankList.stream().map(rankItem -> {
                return UserItemVO.builder()
                        .userId(rankItem.getFemaleUid())
                        .userName(rankItem.getFemaleUserName())
                        .icon(rankItem.getFemaleAvatar())
                        .ballHDSchedule(hallowmasDayHDConstant.getTaskList(userId, rankItem.getFemaleUid()))
                        .build();
            }).collect(Collectors.toList());
        } else {
            return rankList.stream().map(rankItem -> {
                return UserItemVO.builder()
                        .userId(rankItem.getMaleUid())
                        .userName(rankItem.getMaleUserName())
                        .icon(rankItem.getMaleAvatar())
                        .ballHDSchedule(hallowmasDayHDConstant.getTaskList(userId, rankItem.getMaleUid()))
                        .build();
            }).collect(Collectors.toList());
        }
    }

    /**
     * 舞会领取
     */
    public Boolean ballDrawReward(String activityCode, String ballHDKey, Long toUid) {
        log.info("ballDrawReward activityCode:{}, ballHDKey:{}, toUid:{}", activityCode, ballHDKey, toUid);
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            return Boolean.FALSE;
        }
        Long userId = user.getUserId();
        // 校验 ballHDKey 是否合法与状态
        if (HallowmasDayHDConstant.BallEnum.getByCode(ballHDKey) == null) {
            log.warn("ballHDKey not part of the event:{}", ballHDKey);
            return Boolean.FALSE;
        }

        UserVO userVO = feignUserService.getBasic(userId, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO)) {
            log.warn("userVO is null uid:{}", userId);
            return null;
        }

        if (Objects.isNull(toUid)) {
            String rankKey = String.format(USER_OTHER_RANK_KEY, userId);
            // 这个时候走的是获取亲密度最高的两人
            RankVO rank = this.getRank(RankContext.builder()
                    .activityCode(ACTIVITY_CODE)
                    .rankKey(rankKey)
                    .rankLen(1L)
                    .type(RankContext.RankType.cp)
                    .build());
            log.info("get ballDrawReward rankVO {}", rank);
            if (rank == null) {
                log.warn("the list is null and cannot be claimed");
                return Boolean.FALSE;
            }
            if (rank.getCpRankItemList().isEmpty()) {
                log.warn("the getCpRankItemList is null");
                return Boolean.FALSE;
            }
            log.debug("the getCpRankItemList get 0:{}", rank.getCpRankItemList().get(0));
            toUid = SexType.MAN.equals(userVO.getSex()) ? rank.getCpRankItemList().get(0).getFemaleUid() : rank.getCpRankItemList().get(0).getMaleUid();
            log.info("the ballDrawReward toUid:{}", toUid);
            if (Objects.isNull(toUid)) {
                log.warn("THE ID OF THE PEER PARTY IS NULL");
                return Boolean.FALSE;
            }
        }
        // 校验 是否已领取 或未达标
        String makeupKey = String.format(MAKEUP_PARTY_KEY, AppUtil.splicUserId(userId, toUid), ballHDKey);
        String orElse = Optional.ofNullable(redisManager.getString(makeupKey)).orElse(StatusEnum.NOT_FINISHED.getStatus());
        if (!StatusEnum.REWARDED_AVAILABLE.getStatus().equals(orElse)) {
            log.warn("userId:{} not finished task:{}", userId, ballHDKey);
            return Boolean.FALSE;
        }
        // 领取奖励 发放奖励 双方都发放
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, getBallHDRewardSceneCode(userId, ballHDKey));
        List<ScenePrizeDO> scenePrizeDOListTo = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, getBallHDRewardSceneCode(toUid, ballHDKey));
        log.info("scenePrizeDOList {}, scenePrizeDOListTo:{}", JSON.toJSONString(scenePrizeDOList), JSON.toJSONString(scenePrizeDOListTo));
        if (CollectionUtils.isEmpty(scenePrizeDOList) || CollectionUtils.isEmpty(scenePrizeDOListTo)) {
            log.warn("the gift configuration is incorrect");
            return Boolean.FALSE;
        }
        sendBall(userId, scenePrizeDOList);
        sendBall(toUid, scenePrizeDOListTo);

        // 修改状态
        redisManager.set(makeupKey, StatusEnum.COMPLETED.getStatus(), DateUtil.ONE_MONTH_SECOND);
        ScenePrizeDO scenePrizeDO = scenePrizeDOList.get(0);
        ScenePrizeDO scenePrizeDOTo = scenePrizeDOListTo.get(0);
        trackManager.allActivityReceiveAward(userId, "halloween_party", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), 1);
        trackManager.allActivityReceiveAward(toUid, "halloween_party", scenePrizeDOTo.getPrizeValue(), scenePrizeDOTo.getPrizeValueGold(), 1);

        // 校验是否领取完毕，清零循环
        if (isAllBallsCompleted(userId, toUid)) {
            hallowmasDayHDConstant.initTaskList(userId, toUid);
            redisManager.delete(String.format(MAKEUP_PARTY_KEY_FORMAT, AppUtil.splicUserId(userId, toUid)));
        }
        return Boolean.TRUE;
    }

    private boolean isAllBallsCompleted(Long userId, Long toUid) {
        int count = 0;
        for (BallEnum ballEnum : BallEnum.values()) {
            String makeupKeyItem = String.format(MAKEUP_PARTY_KEY, AppUtil.splicUserId(userId, toUid), ballEnum.getBallHDKey());
            String status = Optional.ofNullable(redisManager.getString(makeupKeyItem)).orElse(StatusEnum.NOT_FINISHED.getStatus());
            if (StatusEnum.COMPLETED.getStatus().equals(status)) {
                count++;
            }
        }
        return count == 5;
    }

    private void sendBall(Long userId, List<ScenePrizeDO> scenePrizeDOList) {
        String msg = "您在“万圣奇妙夜”的“万圣舞会”中获得%s一个，已经下发，注意查收哦~";
        sendPrizeManager.sendPrize(
                BaseParam.builder()
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .uid(userId)
                        .build(),
                scenePrizeDOList.stream().map(SendPrizeDTO::of).collect(Collectors.toList())
        );
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                userId,
                String.format(msg, scenePrizeDOList.get(0).getPrizeDesc())
        );
    }

    /**
     * 获取舞会奖励sceneCode
     */
    public String getBallHDRewardSceneCode(Long uid, String ballHDKey) {
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).getData();
        if (Objects.isNull(userVO)) {
            log.warn("userVO is null uid:{}", uid);
            return null;
        }
        String sex = SexType.MAN.equals(userVO.getSex()) ? "man" : "woman";
        return String.format(MAKEUP_PARTY_SCENE_CODE, sex, ballHDKey);
    }

/******************  定时任务  ******************/


    /**
     * 发放捣蛋派对
     */
    public void sendDanceLeaderboard() {
//        String redisKey = "ump:family_tournament_ft:send_sum_ft:total_rank";
//        if (!Boolean.TRUE.equals(redisManager.setnx(String.format(redisKey, DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
//            return;
//        }

        //总榜
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "hd-dance-gift");
        log.info("dance scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            return;
        }

        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(ACTIVITY_CODE)
                .rankKey(DANCE_PARTY_KEY)
                .rankLen(5L)
                .type(RankContext.RankType.user)
                .build());
        log.info("sendDanceLeaderboard rankVO {}", rankVO);
        if (rankVO == null) {
            return;
        }
        for (RankItem rankItem : rankVO.getRankList()) {
            if (rankItem.getValue() < MIN_SUM_RANK_VALUE) {
                log.info("uid {} rankItem {} 没有奖励", rankItem.getId(), rankItem);
                continue;
            }
            List<ScenePrizeDO> scenePrizeDOs = scenePrizeDOList.stream().filter(scenePrizeDO ->
                    (Objects.equals(rankItem.getRank(), scenePrizeDO.getPrizeBelongToRank()))).collect(Collectors.toList());
            log.info("TreasureHall#sendPrize rankItem {} scenePrize {}", rankItem, scenePrizeDOs);
            if (CollectionUtils.isEmpty(scenePrizeDOs)) {
                continue;
            }
            sendPrizeManager.sendPrize(
                    BaseParam.builder()
                            .appId(ServicesAppIdEnum.lanling.getAppId())
                            .unionId(ServicesAppIdEnum.lanling.getUnionId())
                            .uid(rankItem.getId())
                            .build(),
                    scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId())).collect(Collectors.toList())
            );
            ScenePrizeDO scenePrizeItem = scenePrizeDOs.get(0);
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    rankItem.getId(),
                    String.format("恭喜您在“万圣奇妙夜”活动中，荣获%s名，获得%s有效期14天1个，礼物已经下发至您的背包，快去查收吧~", rankItem.getRank(), scenePrizeItem.getPrizeDesc())
            );
            for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
                trackManager.allActivityReceiveAward(rankItem.getId(), "rank",
                        scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum());
            }
        }
    }


}