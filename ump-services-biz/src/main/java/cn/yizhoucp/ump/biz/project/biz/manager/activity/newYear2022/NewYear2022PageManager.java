package cn.yizhoucp.ump.biz.project.biz.manager.activity.newYear2022;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.PackageQueryConditionDTO;
import cn.yizhoucp.product.dto.UserPackageDetailDTO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.newYear2022.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.newYear2022.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountFeignService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.NewYear2022Constant.*;

/**
 * 新年上上签
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class NewYear2022PageManager {

    @Resource
    private UserAccountFeignService userAccountFeignService;
    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private NewYear2022BizManager newYear2022BizManager;

    /**
     * 获取首页信息
     *
     * @param param
     * @return
     */
    public IndexVO getIndex(BaseParam param) {

        // 获取签数量
        List<Integer> ticketNum = getTicketNum(param);
        // 获取财富榜用户信息
        RankItem userRichRankInfo = getUserRankInfo(param, RICH_RANK);
        // 获取财富榜
        List<RankItem> richRank = getRank(param, RICH_RANK, 100L);
        // 获取魅力榜用户信息
        RankItem userCharmRankInfo = getUserRankInfo(param, CHARM_RANK);
        // 获取魅力榜
        List<RankItem> charmRank = getRank(param, CHARM_RANK, 100L);

        return IndexVO.builder()
                .ticketNum(ticketNum)
                .richRank(richRank)
                .userRichRankInfo(userRichRankInfo)
                .charmRank(charmRank)
                .userCharmRankInfo(userCharmRankInfo)
                .build();
    }

    /**
     * 获取奖励页信息
     *
     * @param param
     * @return
     */
    public DrawReturn getPrizeInfo(BaseParam param) {
        JSONObject prize = (JSONObject) redisManager.sPop(String.format(PRIZE_POP_SET, param.getUid()));
        log.debug("prize:{}", JSON.toJSONString(prize));
        if (Objects.isNull(prize)) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "无奖励信息");
        }
        DrawReturn drawReturn = prize.toJavaObject(DrawReturn.class);
        log.debug("drawReturn:{}", JSON.toJSONString(drawReturn));
        return drawReturn;
    }

    private RankItem getUserRankInfo(BaseParam param, String key) {
        // 获取家族列表
        Long rank = redisManager.reverseRank(key, param.getUid().toString());
        if (Objects.isNull(rank)) {
            rank = -1L;
        } else {
            ++rank;
        }
        // 获取数值
        Long value = Optional.ofNullable(redisManager.score(key, param.getUid().toString())).orElse(0d).longValue();
        // 获取用户信息
        UserBaseVO user = userRemoteService.getBasicAll(param.getAppId(), param.getUid(), Boolean.TRUE);
        return RankItem.builder()
                .rank(rank)
                .uid(user.getId())
                .name(user.getName())
                .icon(user.getAvatar())
                .value(value).build();
    }

    private List<RankItem> getRank(BaseParam param, String key, Long limit) {
        List<RankItem> rank = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(key, 0, Double.MAX_VALUE, 0, limit);
        log.debug("newYear 2022排行榜详情 key:{}, rank:{}", key, JSON.toJSONString(typedTuples));
        Long index = 0L;
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            ++index;
            if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                continue;
            }
            Long uid = Long.parseLong(typedTuple.getValue().toString());
            Integer value = typedTuple.getScore().intValue();
            rank.add(RankItem.builder().rank(index).uid(uid).value(value.longValue()).build());
        }
        if (CollectionUtils.isEmpty(rank)) {
            return rank;
        }

        // 补充用户信息
        rank.parallelStream().forEach(r -> {
            UserBaseVO user = userRemoteService.getBasicAll(param.getAppId(), r.getUid(), Boolean.TRUE);
            if (Objects.isNull(user)) {
                return;
            }
            r.setName(user.getName());
            r.setIcon(user.getAvatar());
        });
        return rank;
    }

    private List<Integer> getTicketNum(BaseParam param) {
        List<Integer> ticketNum = Lists.newArrayList();
        try {
            PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder().uid(param.getUid())
                    .bizIdList(GIFT_LIST)
                    .bizType(UserPackageBizType.GIFT.getCode())
                    .packageScene(newYear2022BizManager.getActivityCode())
                    .build();
            Map<String, UserPackageDetailDTO> packageMap = userPackageFeignService.getPackageDetailMapByCondition(queryParam).successData();
            log.debug("packageMap:{}", JSON.toJSONString(packageMap));
            if (MapUtils.isEmpty(packageMap)) {
                return Lists.newArrayList(0, 0, 0, 0, 0, 0);
            }
            for (NewYear2022Constant.TICKET item : NewYear2022Constant.TICKET.values()) {
                ticketNum.add(Optional.ofNullable(packageMap.get(item.name())).map(UserPackageDetailDTO::getAvailableNum).orElse(0L).intValue());
            }
        } catch (Exception e) {
            log.error("newYear2022 获取页面信息异常", e);
            throw new ServiceException(ErrorCode.MISS_PARAM, "系统繁忙，请稍后再试");
        }
        return ticketNum;
    }

}
