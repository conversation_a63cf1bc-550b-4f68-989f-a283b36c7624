package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.starter.cdn.enums.CdnEnum;
import cn.yizhoucp.starter.cdn.util.CdnUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 奖池公示信息
 *
 * @author: lianghu
 */
@Table(name = "draw_pool_post_info")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrawPoolPostDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 应用编号 */
    private Long appId;
    /** 活动编号 */
    private String activityCode;
    /** 奖池编号 */
    private String poolCode;
    /** 奖池名称 */
    private String poolName;
    /** 主标题 */
    private String firstTitle;
    /** 副标题 */
    private String secondTitle;
    /** 背景图片 */
    private String bgImg;
    /** 状态 */
    private Integer status;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新时间 */
    private LocalDateTime updateTime;

    public DrawPoolPostDO setDefault() {
        status = 1;
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
        return this;
    }

    public String getBgImg() {
        return CdnUtil.getForceUrl(bgImg, CdnEnum.resCdn);
    }

    public void setBgImg(String bgImg) {
        this.bgImg = CdnUtil.setUrl(bgImg);
    }

}
