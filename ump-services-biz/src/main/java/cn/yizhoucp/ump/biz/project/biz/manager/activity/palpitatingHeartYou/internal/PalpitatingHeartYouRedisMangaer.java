package cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou.internal;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.PalpitatingHeartYouConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PalpitatingHeartYouRedisMangaer {
    @Resource
    private RedisManager redisManager;

    public String getMessageTemplateCache(String activityCode, String accompanyRankKey) {
        String key=String.format(PalpitatingHeartYouConstant.MESSAGE_TEMPLATE_CACHE_KEY,activityCode,accompanyRankKey);
        return redisManager.getString(key);
    }
}
