package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Table(name = "astrology_value")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AstrologyValueDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long userId;

    private Long balance;

    public static AstrologyValueDO ofByUserId(Long userId) {
        return AstrologyValueDO.builder()
                .userId(userId)
                .balance(0L).build();
    }
}
