package cn.yizhoucp.ump.biz.project.web.rest.controller;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.AstrologerChallengeConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.CleanDataManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AliceInWonderland.AliceInWonderlandJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.MainChineseNewYear2025RankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.activity520.Activity520JobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrologerChallenge.AstrologerChallengeRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaTour.StarSeaTourRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSpring.StarSpringRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starWishCardCollectionGame.StarWishCardCollectionGameRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrologyAdventure.AstrologyAdventureRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.auctionKing.AuctionKingRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.christmasBell.ChristmasBellSupport;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.clickToGold.ClickToGoldJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.courtBeautyBiography.CourtBeautyBiographyRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.crazygift.CrazyGiftJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.doubleEgg2023.DoubleEgg2023BizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.doubleEgg2023.DoubleEgg2023RankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.dreamingIntoTheGalaxy.DreamingIntoTheGalaxyJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.familyCompetition.FamilyCompetitionJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.familyKey.FamilyKeyManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.floweringDream.FloweringDreamJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddessFestival2023.GoddessFestival2023BizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.hallowmasDayHD.HallowmasDayHDJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.LuckyBagRuleDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.kingAdvanced.KingAdvancedJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.lanternFestival2025.LanternFestivalSupport;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland.LegendaryNeverlandBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.LoveInBloomFestivalRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.LoveInProgressRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveLetterLY.LoveLetterLYRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.lovestory.LoveStoryTakePrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBag.LuckyBagActivityManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBagFight.LuckBagFightAsyncManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.magpie2023.Magpie2023RankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.midAutumn2023.MidAutumn2023RankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.milesOfLove.MilesOfLoveRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.miracleWinterSnow.MiracleWinterSnowRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.mistyStarContinent.MistyStarContinentRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.MoonAboveTheSeaRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.mysteryGuestMG.MysteryGuestMGJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.palpitatingHeartYou.PalpitatingHeartYouJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.qixiActivity.QiXiFestivalJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.RushSkyBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.sakaWeekStar.WeekStarBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.seasonalFestival.SeasonalFestivalRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.springFestival2024.SpringFestival2024RankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.sweetLoveCupid.SweetLoveCupidButtonManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.swordsman.SwordsmanJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thanksgivingBattle.ThanksgivingBattleBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thanksgivingBattle.ThanksgivingBattleRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.ThePrincessDiariesRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureInSecret.TreasureInSecretRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.twins.TwinsPageManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.twins.TwinsRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wlzb.WuLinHegemonyBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.worldAnnounceLoveYou.WorldAnnounceLoveYouIndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace.AppAdSpaceAdminManager;
import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainActivityManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.JimuManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.lovepromise.LovePromiseManager;
import cn.yizhoucp.ump.biz.project.biz.manager.officialCombine.OfficialCombineBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.officialCombine.OfficialCombineV2Manager;
import cn.yizhoucp.ump.biz.project.biz.manager.officialCombine.SweetCabinAsyncManager;
import cn.yizhoucp.ump.biz.project.biz.manager.officialCombine.SweetCabinTaskManager;
import cn.yizhoucp.ump.biz.project.biz.manager.redPacket.RedPacketManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userGroup.UserGroupManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
@RestController
public class JobController {

    @Resource
    private SpringFestival2024RankManager springFestival2024RankManager;
    @Resource
    private JimuManager jimuManager;
    @Resource
    private StarWishCardCollectionGameRankManager starWishCardCollectionGameRankManager;
    @Resource
    private DoubleEgg2023BizManager doubleEgg2023BizManager;
    @Resource
    private DoubleEgg2023RankManager doubleEgg2023RankManager;
    @Resource
    private ThanksgivingBattleBizManager thanksgivingBattleBizManager;
    @Resource
    private ThanksgivingBattleRankManager thanksgivingBattleRankManager;
    @Resource
    private AppAdSpaceAdminManager appAdSpaceAdminManager;
    @Resource
    private FamilyKeyManager familyKeyManager;
    @Resource
    private RedPacketManager redPacketManager;
    @Resource
    private OfficialCombineBizManager officialCombineBizManager;
    @Resource
    private LuckyBagActivityManager luckyBagActivityManager;
    @Resource
    private ActivityManager activityManager;
    @Resource
    private UserGroupManager userGroupManager;
    @Resource
    private RushSkyBizManager rushSkyBizManager;
    @Resource
    private LuckyBagRuleDrawManager luckyBagRuleDrawManager;
    @Resource
    private LuckBagFightAsyncManager luckBagFightAsyncManager;
    @Resource
    private GoddessTrainActivityManager goddessTrainActivityManager;
    @Resource
    private CleanDataManager cleanDataManager;
    @Resource
    private ClickToGoldJobManager clickToGoldJobManager;
    @Resource
    private GoddessFestival2023BizManager goddessFestival2023BizManager;
    @Resource
    private WuLinHegemonyBizManager wuLinHegemonyBizManager;
    @Resource
    private WeekStarBizManager weekStarBizManager;
    @Resource
    private Activity520JobManager activity520JobManager;
    @Resource
    private TwinsPageManager twinsPageManager;
    @Resource
    private TwinsRankManager twinsRankManager;
    @Resource
    private AstrologerChallengeRankManager astrologerChallengeRankManager;
    @Resource
    private Magpie2023RankManager magpie2023RankManager;
    @Resource
    private MidAutumn2023RankManager midAutumn2023RankManager;
    @Resource
    private AstrologyAdventureRankManager astrologyAdventureRankManager;

    @Resource
    private LovePromiseManager lovePromiseManager;
    @Resource
    private MistyStarContinentRankManager mistyStarContinentRankManager;
    @Resource
    private StarSeaTourRankManager starSeaTourRankManager;

    @Resource
    private AliceInWonderlandJobManager aliceInWonderlandJobManager;

    @Resource
    private KingAdvancedJobManager kingAdvancedJobManager;

    @Resource
    private QiXiFestivalJobManager qiXiFestivalJobManager;

    @Resource
    private FloweringDreamJobManager floweringDreamJobManager;

    @Resource
    private DreamingIntoTheGalaxyJobManager dreamingIntoTheGalaxyJobManager;

    @Resource
    private OfficialCombineV2Manager OfficialCombineV2Manager;

    @Resource
    private SweetCabinAsyncManager sweetCabinAsyncManager;

    @Resource
    private CrazyGiftJobManager crazyGiftJobManager;

    @Resource
    private FamilyCompetitionJobManager familyCompetitionJobManager;

    @Resource
    private PalpitatingHeartYouJobManager palpitatingHeartYouJobManager;

    @Resource
    private LoveStoryTakePrizeManager loveStoryTakePrizeManager;

    @Resource
    private HallowmasDayHDJobManager hallowmasDayHDJobManager;

    @Resource
    private WorldAnnounceLoveYouIndexManager worldAnnounceLoveYouIndexManager;

    @Resource
    private MysteryGuestMGJobManager mysteryGuestMGJobManager;

    @Resource
    private SwordsmanJobManager swordsmanJobManager;

    @Resource
    private LoveLetterLYRankManager loveLetterLYRankManager;

    @Resource
    private MiracleWinterSnowRankManager miracleWinterSnowRankManager;
    @Resource
    private SweetLoveCupidButtonManager sweetLoveCupidButtonManager;
    @Resource
    private CourtBeautyBiographyRankManager courtBeautyBiographyRankManager;
    @Resource
    private LegendaryNeverlandBizManager legendaryNeverlandBizManager;
    @Resource
    private MilesOfLoveRankManager milesOfLoveRankManager;

    @Resource
    private ChristmasBellSupport christmasBellSupport;
    @Resource
    private LanternFestivalSupport lanternFestivalSupport;

    @Resource
    private TreasureInSecretRankManager treasureInSecretRankManager;
    @Resource
    private MoonAboveTheSeaRankManager moonAboveTheSeaRankManager;

    @Resource
    private SweetCabinTaskManager sweetCabinTaskManager;
    @Resource
    private StarSpringRankManager starSpringRankManager;
    @Resource
    private MainChineseNewYear2025RankManager mainChineseNewYear2025RankManager;
    @Resource
    private SeasonalFestivalRankManager seasonalFestivalRankManager;
    @Resource
    private LoveInBloomFestivalRankManager loveInBloomFestivalRankManager;
    @Resource
    private LoveInProgressRankManager loveInProgressRankManager;
    @Resource
    private ThePrincessDiariesRankManager thePrincessDiariesRankManager;

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/spring_festival_2024/heart_beat_board_send_prize")
    public Result<Boolean> springFestival2024HeartBeatBoardSendPrize() {
        return Result.successResult(springFestival2024RankManager.heartBeatBoardSendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/spring_festival_2024/pray_board_send_prize")
    public Result<Boolean> springFestival2024PrayBoardSendPrize() {
        return Result.successResult(springFestival2024RankManager.prayBoardSendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/spring_festival_2024/send_sign_prize1")
    public Result<Boolean> springFestival2024SendSignPrize1() {
        return Result.successResult(springFestival2024RankManager.sendSignPrize1());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/spring_festival_2024/send_sign_prize2")
    public Result<Boolean> springFestival2024SendSignPrize2() {
        return Result.successResult(springFestival2024RankManager.sendSignPrize2());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/jimu/send_prize")
    public Result<Boolean> sendPrize(String activityCode, String rankKey) {
        return Result.successResult(jimuManager.sendPrize(RankContext.builder().activityCode(activityCode).rankKey(rankKey).build()));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/jimu/star_wish_card_collection_game/send_prize")
    public Result<Boolean> starWishCardCollectionGameSendPrize() {
        return Result.successResult(starWishCardCollectionGameRankManager.sendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/jimu/star_sea_tour/send_prize")
    public Result<Boolean> starSeaTourSendPrize(String rankKey) {
        return Result.successResult(starSeaTourRankManager.starSeaTourSendPrize(rankKey));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/double_egg2023/unlock_scene_send_prize")
    public Result<Boolean> doubleEgg2023UnlockSceneSendPrize() {
        return Result.successResult(doubleEgg2023BizManager.unlockSceneSendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/double_egg2023/send_prize")
    public Result<Boolean> doubleEgg2023SendPrize() {
        return Result.successResult(doubleEgg2023RankManager.sendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/thanksgiving_battle/unlock_scene_send_prize")
    public Result<Boolean> thanksgivingBattleUnlockSceneSendPrize() {
        return Result.successResult(thanksgivingBattleBizManager.thanksgivingBattleUnlockSceneSendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/thanksgiving_battle/send_prize")
    public Result<Boolean> thanksgivingBattleSendPrize() {
        return Result.successResult(thanksgivingBattleRankManager.sendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/misty_star_continent/send-prize")
    public Result<Boolean> mistyStarContinentSendPrize() {
        return Result.successResult(mistyStarContinentRankManager.sendPrize());
    }

    @Resource
    private AuctionKingRankManager auctionKingRankManager;

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/mid-autumn2023/send-prize")
    public Result<Boolean> midAutumn2023SendPrize() {
        return Result.successResult(midAutumn2023RankManager.sendPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/astrology-adventure/send-prize")
    public Result<Boolean> astrologyAdventureSendPrize(String date) {
        return Result.successResult(astrologyAdventureRankManager.sendPrize(date));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/magpie2023/send-prize")
    public Result<Boolean> magpie2023SendPrize() {
        return Result.successResult(magpie2023RankManager.sendYesterdayPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/magpie2023/send-couple-board-prize")
    public Result<Boolean> magpie2023SendCoupleBoardPrize() {
        return Result.successResult(magpie2023RankManager.sendCoupleBoardPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/magpie2023/send-sign-prize")
    public Result<Boolean> magpie2023SendSignPrize() {
        return Result.successResult(magpie2023RankManager.sendSignPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/astrologer-challenge/send-prize")
    public Result<Boolean> astrologerChallengeSendPrize() {
        return Result.successResult(astrologerChallengeRankManager.sendYesterdayPrize());
    }

    /**
     * 更新榜单信息
     *
     * @param params
     * @return
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/astrologer-challenge/update-top1")
    public Result<Boolean> astrologerChallengeUpdateTop1(String params) {
        astrologerChallengeRankManager.accumulatedDuration(String.format(AstrologerChallengeConstant.TODAY_RANK_KEY, ActivityTimeUtil.getToday(ActivityCheckListEnum.ASTROLOGER_CHALLENGE.getCode()), 10), "ump:astrologer:rank:accumulatedDuration:astrologer10");
        astrologerChallengeRankManager.accumulatedDuration(String.format(AstrologerChallengeConstant.TODAY_RANK_KEY, ActivityTimeUtil.getToday(ActivityCheckListEnum.ASTROLOGER_CHALLENGE.getCode()), 100), "ump:astrologer:rank:accumulatedDuration:astrologer100");
        return Result.successResult(Boolean.TRUE);
    }

    /**
     * 开启守护双子座全服星运时刻
     *
     * @param params
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/twins/open-lucky-time")
    public Result<Boolean> openTwinsLuckyTime(String params) {
        return Result.successResult(twinsPageManager.openGlobalLuckyTime());
    }

    /**
     * 全服星运时刻结束更改礼盒静态图
     *
     * @param params
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/twins/update-gift-icon")
    public Result<Boolean> updateGiftIcon(String params) {
        return Result.successResult(twinsPageManager.updateGiftIcon());
    }

    /**
     * 守护双子座 下发榜单奖励
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/twins/send-rank-prize")
    public Result<Boolean> twinsSendRankPrize(String params) {
        return Result.successResult(twinsRankManager.twinsSendRankPrize(params));
    }

    /**
     * 周星礼物活动周结算
     *
     * @param params
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/week-star/settle-down")
    public Result<Boolean> weekStarSettleDown(String params) {
        BaseParam param = BaseParam.builder().appId(ServicesAppIdEnum.chatie.getAppId()).unionId(ServicesAppIdEnum.chatie.getUnionId()).build();
        return Result.successResult(weekStarBizManager.rankSettleDown(param));
    }

    /**
     * 武林争霸定时任务
     *
     * @param params
     * @return FightIndexVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/wu-lin-hegemony/create")
    public Result<Boolean> wuLinHegemonyJob(String params) {
        BaseParam param = BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).build();
        return Result.successResult(wuLinHegemonyBizManager.startGame(param));
    }

    /**
     * 冷数据移除
     *
     * @param params
     * @return FightIndexVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/clean-data/activity-user-mission")
    public Result<Boolean> cleanActivityUserMission(String params) {
        return Result.successResult(cleanDataManager.activityUserMissionClean());
    }

    /**
     * 女生预约 & 女生作业活动续期
     *
     * @param params
     * @return FightIndexVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/goddess-train/refresh")
    public Result<Boolean> refreshGoddessCycleActivity(String params) {
        return Result.successResult(goddessTrainActivityManager.refreshActivity(params));
    }

    /**
     * 福袋双旦刷新库存
     *
     * @param params
     * @return FightIndexVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/lucky-bag-fight/refresh")
    public Result<Boolean> refreshStocks(String params) {
        return Result.successResult(luckBagFightAsyncManager.refreshStock(params));
    }

    /**
     * 冲上云霄每日 Pk 分组任务
     *
     * @return null
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/rush-sky/daily-pk-handle")
    public Result rushSkyDailyPkHandle(String unionId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            rushSkyBizManager.dailyPkHandle(unionId);
            return null;
        });
    }

    /**
     * 冲上云霄每日定时任务
     *
     * @return null
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/rush-sky/daily-handle")
    public Result rushSkyDailyHandle(String unionId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            rushSkyBizManager.dailyHandle(unionId);
            return null;
        });
    }

    /**
     * 更新每日例行用户分群
     *
     * @return null
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/refresh-user-group")
    public Result updateUserGroupCache(Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            userGroupManager.refreshUserGroupCache(appId);
            return null;
        });
    }

    /**
     * 定时上线过期活动
     *
     * @return null
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/activity-job-handle")
    public Result putOnOrOffActivity(Long appId, String unionId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            activityManager.activityJobHandle();
            return null;
        });
    }

    /**
     * 定时上下架广告位信息
     *
     * @return null
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/put-on-or-off-ad-space")
    public Result putOnOrOffAdSpace(Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            appAdSpaceAdminManager.putOnOrOffAdSpace(appId);
            return null;
        });
    }

    /**
     * 家族钥匙大赛日结算
     *
     * @return String
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/family-key-daily-settlement")
    public Result<String> familyKeyDailySettlement() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            familyKeyManager.dailySettlementHandle();
            return "ok";
        });
    }

    /**
     * 家族钥匙大赛周结算
     *
     * @return String
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/family-key-weekly-settlement")
    public Result<String> familyKeyWeeklySettlement() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            familyKeyManager.weeklySettlementHandle();
            return "ok";
        });
    }

    /***
     * 处理过期红包
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/handle-expire-red-packet")
    public Result handleExpireRedPacket(Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            redPacketManager.handleExpireRedPacket(appId);
            return "ok";
        });
    }

    /**
     * 福袋抽奖活动 榜单奖励定时任务
     *
     * @param appId
     * @return
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/lucky-bag-rank-reward")
    public Result luckyBagRankRewards(Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            luckyBagActivityManager.doLuckyBagRankRewardsAction(appId);
            return "ok";
        });
    }

    /**
     * 自动过期
     *
     * @return
     * @para
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/official-combine/auto-expired")
    public Result<Boolean> autoExpired() {
        return Result.successResult(officialCombineBizManager.autoExpired());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/add-history-track")
    public Result addHistoryTrack() {
        officialCombineBizManager.addHistoryCombineTrack();
        return Result.successResult();
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/luck-draw-return-rate")
    public Result calUserReturnRate() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            luckyBagRuleDrawManager.calUserReturnRate();
            return "ok";
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/lucky-bag-fight-pre-show")
    public Result fightPreShow() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            luckBagFightAsyncManager.handReadOpenShow();
            return "ok";
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/lucky-bag-fight-no-killed-reward")
    public Result noKilledReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            luckBagFightAsyncManager.handleNotKillData();
            return "ok";
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/click-to-gold/notify-user-pacakge-info")
    public Result<Boolean> notifyUserPackageInfo() {
        return Result.successResult(clickToGoldJobManager.notifyUserPackageInfo());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/click-to-gold/send-rank-reward")
    public Result<Boolean> sendRankReward() {
        return Result.successResult(clickToGoldJobManager.sendRankReward());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/goddess-festival-2023/send-rank-reward")
    public Result<Boolean> issueAwardsJob() {
        return Result.successResult(goddessFestival2023BizManager.issueAwardsJob());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/activity-520/send-top10-prize")
    public Result<Boolean> sendTop10Prize() {
        return Result.successResult(activity520JobManager.sendTop10Prize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity/auction-king/send-reward")
    public Result<Boolean> auctionReward() {
        return Result.successResult(auctionKingRankManager.sendReward());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/job/ump/incomplete-engagement-automatic-formation")
    public Result<Boolean> incompleteEngagementAutomaticFormation() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> lovePromiseManager.incompleteEngagementAutomaticFormation());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/activity-alice/send-reward")
    public Result<Boolean> aliceActivitySendReward(String stage) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            aliceInWonderlandJobManager.sendPrize(stage);
            return true;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/qixi_festival/send-love-reward")
    public Result<Boolean> qixiFestivalSendLoveReward(String date) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            qiXiFestivalJobManager.sendLoveRank(date);
            return true;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/qixi_festival/send-qiluo-reward")
    public Result<Boolean> qixiFestivalSendQiLuoReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            qiXiFestivalJobManager.sendQiLuoRank();
            return true;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/qixi_festival/send-sign-all-reward")
    public Result<Boolean> qixiFestivalSendSignAllReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            qiXiFestivalJobManager.sendSignAll();
            return true;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/king-advanced/send-king")
    Result<Boolean> kingAdvancedKingGift() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                kingAdvancedJobManager.sendKingPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/king-advanced/send-ultimate")
    Result<Boolean> kingAdvancedUltimateGift() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                kingAdvancedJobManager.sendUltimatePrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/flowering-dream/send-envoy")
    Result<Boolean> dreamEnvoySendPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                floweringDreamJobManager.sendEnvoyPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/flowering-dream/send-theater")
    Result<Boolean> dreamTheaterSendPrize(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                floweringDreamJobManager.sendTheaterPrize(params));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/dreaming-into-the-galaxy/send-prize")
    Result<Boolean> dreamingIntoTheGalaxySendPrize(String date) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                dreamingIntoTheGalaxyJobManager.sendPrize(date));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/crazy-gift/refresh-task")
    Result<Boolean> refreshDailyTaskJob() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            crazyGiftJobManager.refreshDailyTaskJob();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/crazy-gift/send-prize")
    Result<Boolean> sendCrazyGiftRankPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            crazyGiftJobManager.sendCrazyGiftRankPrize();
            return Boolean.TRUE;
        });
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/palpitating-heart-you/send-accompany")
    Result<Boolean> sendAccompanyLeaderboard() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                palpitatingHeartYouJobManager.sendAccompanyLeaderboard());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/palpitating-heart-you/send-heart")
    Result<Boolean> sendHeartbeatLeaderboard() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                palpitatingHeartYouJobManager.sendHeartbeatLeaderboard());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/official-combine/monthly-rank-prize")
    Result<Boolean> monthlyRankPrize(String date) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
        {
            OfficialCombineV2Manager.sendRankPrize();
            return true;
        });
    }

    /**
     * 定时任务下发恋屋币
     *
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/sweet-cabin/output-lh")
    Result<Boolean> timedOutputLhValue() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            sweetCabinAsyncManager.timedOutputLhValue(null);
            return Boolean.TRUE;
        });
    }

    /**
     * 定时任务处理恋屋币衰减
     *
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/sweet-cabin/lh-decay")
    Result<Boolean> timedLhDecay() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            sweetCabinAsyncManager.timedLhDecay();
            return Boolean.TRUE;
        });
    }

    /**
     * 下发排行榜奖励
     *
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/sweet-cabin/send-ranking-prize")
    Result<Boolean> sendRankPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            sweetCabinAsyncManager.sendRankPrize();
            return Boolean.TRUE;
        });
    }

    /**
     * 处理所有历史数据
     *
     * @return Boolean
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/sweet-cabin/handle-history-cabin")
    Result<Boolean> handleHistoryCabin() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            sweetCabinAsyncManager.handleHistoryCabin(null);
            return Boolean.TRUE;
        });
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/love-story/send-ranking-prize")
    Result<Boolean> loveStoryRankPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            loveStoryTakePrizeManager.takeRankPrizeJob();
            return Boolean.TRUE;
        });
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/family-tournament-ft/send-day")
    Result<Boolean> sendDayFTLeaderboard(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                familyCompetitionJobManager.sendDayFTLeaderboard(params));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/family-tournament-ft/send-sum")
    Result<Boolean> sendSumFTLeaderboard() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                familyCompetitionJobManager.sendSumFTLeaderboard());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/family-tournament-ft/send-family")
    Result<Boolean> sendFamilyFTLeaderboard(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                familyCompetitionJobManager.sendFamilyFTLeaderboard(params));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/hallowmas-day-hd/send-dance")
    Result<Boolean> sendDanceLeaderboard(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                hallowmasDayHDJobManager.sendDanceLeaderboard());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/world-announce-love-you/send-gift-handle")
    Result<Boolean> sendGiftHandle(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                worldAnnounceLoveYouIndexManager.sendGiftHandle());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/mystery-guest-mg/send-gift-day")
    Result<Boolean> sendDayLeaderboard(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                mysteryGuestMGJobManager.sendDayLeaderboard(params));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/mystery-guest-mg/send-gift-total")
    Result<Boolean> sendTotalLeaderboard() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                mysteryGuestMGJobManager.sendTotalLeaderboard());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/love-letter-ly/send-love-rank")
    Result<Boolean> sendingLoveLetters() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            loveLetterLYRankManager.sendingLoveLetters();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/miracle-winter-snow/send-miracle-rank")
    Result<Boolean> sendMiracleLeaderboard() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            miracleWinterSnowRankManager.sendMiracleLeaderboard();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/swordsman/swordsman-update-ingots")
    Result<Boolean> swordsmanUpdateIngots() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                swordsmanJobManager.swordsmanUpdateIngots());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/swordsman/swordsman-send-npc-msg")
    Result<Boolean> swordsmanSendNpcMsg() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                swordsmanJobManager.swordsmanSendNpcMsg());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/swordsman/swordsman-send-auction-reward")
    Result<Boolean> swordsmanSendAuctionReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                swordsmanJobManager.swordsmanSendAuctionReward());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/swordsman/swordsman-send-activity-total-reward")
    Result<Boolean> swordsmanSendActivityTotalReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                swordsmanJobManager.swordsmanSendActivityTotalReward());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/swordsman/swordsman-clear-no-receive-ingot")
    Result<Boolean> swordsmanClearNoReceiveIngot() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                swordsmanJobManager.swordsmanClearNoReceiveIngot());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/sweet-love-cupid/send-rank")
    Result<Boolean> sweetLoveCupidSendRank() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                sweetLoveCupidButtonManager.sendRankReward());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/court-beauty-biography/send-rank")
    Result<Boolean> sendCourtBeautyBiography() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            courtBeautyBiographyRankManager.sendCourtBeautyBiography();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/legendary-neverland/deduct-zero-strength")
    Result<Boolean> deductZeroStrength(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            legendaryNeverlandBizManager.deductZeroStrength(Integer.valueOf(params));
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/miles-of-love/send-pride-list")
    Result<Boolean> giveOutAPrideList(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            milesOfLoveRankManager.giveOutAPrideList();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/miles-of-love/send-glamour-list")
    Result<Boolean> giveOutGlamourLists(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            milesOfLoveRankManager.giveOutGlamourLists();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/miles-of-love/send-top-like")
    Result<Boolean> giveOutLikeTopThree(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            milesOfLoveRankManager.giveOutLikeTopThree();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/miles-of-love/fill-new-year")
    Result<Boolean> fillNewYearCountdownProgress(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            milesOfLoveRankManager.fillNewYearCountdownProgress();
            return Boolean.TRUE;
        });
    }

    /**
     * 圣诞活动下发奖励
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/christmas-bells/christmas-send-activity-total-reward")
    Result<Boolean> christmasSendActivityTotalReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                christmasBellSupport.christmasSendActivityTotalReward());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/sweet-cabin/renewal-task/system-notice")
    Result<Boolean> systemNotice(String type) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
//            sweetCabinTaskManager.systemNotice(null, type);
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/treasure-in-secret/gift-giving-list")
    Result<Boolean> giftGivingList() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            treasureInSecretRankManager.giftGivingList();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/treasure-in-secret/receive-giving-list")
    Result<Boolean> receiveGivingList() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            treasureInSecretRankManager.receiveGivingList();
            return Boolean.TRUE;
        });
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/star-spring/send-star-spring-day-prize")
    Result<Boolean> sendStarSpringDayPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                starSpringRankManager.sendStarSpringDayPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/star-spring/send-star-spring-sum-prize")
    Result<Boolean> sendStarSpringSumPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                starSpringRankManager.sendStarSpringSumPrize());
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/moonAboveTheSea/sendPrize")
    Result<Boolean> moonAboveTheSeaSendPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            moonAboveTheSeaRankManager.sendRankPrize();
            return Boolean.TRUE;
        });
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/chineseNewYear/sendPrize")
    Result<Boolean> chineseNewYear2025SendPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            mainChineseNewYear2025RankManager.sendRankPrize();
            return Boolean.TRUE;
        });
    }


    /**
     * 元宵活动下发奖励
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/lantern/lantern-festival-send-activity-reward")
    Result<Boolean> lanternFestivalSendActivityReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                lanternFestivalSupport.lanternFestivalSendActivityReward());
    }

    /**
     * 下发奖励
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/love-progress-send-rank-prize")
    Result<Boolean> loveProgressSendRankPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                loveInProgressRankManager.sendRankPrize());
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/seasonal-festival-send-rank-reward")
    Result<Boolean> seasonalFestivalActivityReward(String params) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                seasonalFestivalRankManager.sendRankPrize(params));
    }


    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/love_in_bloom/send-rank-reward")
    Result<Boolean> loveInBloomFestivalSendRankReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                loveInBloomFestivalRankManager.sendRankPrize()
        );

    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/the_princess_diaries/send-rank-reward")
    Result<Boolean> ThePrincessDiariesSendRankReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                thePrincessDiariesRankManager.sendAllRankPrize()
        );

    }






}
