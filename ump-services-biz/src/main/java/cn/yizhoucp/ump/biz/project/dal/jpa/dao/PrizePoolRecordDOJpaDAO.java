package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PrizePoolRecordDO;
import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import javax.transaction.Transactional;

/**
 * 福袋
 *
 * @author: lianghu
 */
public interface PrizePoolRecordDOJpaDAO extends CrudRepository<PrizePoolRecordDO, Long> {

    /**
     * 获取最近可用状态的奖池
     * @param status
     * @return
     */
    PrizePoolRecordDO findTopByTimesTypeAndStatusOrderByIdDesc(Long timesType, CommonStatus status);


    /**
     * 获取指定类型可用状态的奖池
     * @param status
     * @return
     */
    PrizePoolRecordDO findTopByPoolTypeAndStatusOrderByIdDesc(String poolType, CommonStatus status);



    /***
     * 库存扣减
     * @param id
     * @param times
     * @return
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update lucky_bag_record set available_num = available_num - ?2, update_time = now() " +
            "where id = ?1",nativeQuery = true)
    int reducePrizePoolBalance(Long id,Long times);

}
