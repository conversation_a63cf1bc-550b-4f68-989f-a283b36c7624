package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendaryNeverland.common;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

public class LnConstant {

    public static final String ACTIVITY_CODE = "legendary_neverland";

    /* =========== 按钮策略eventCode =========== */

    // 兑换
    public static final String EXCHANGE = "exchange";
    // 命名
    public static final String BE_NAMED = "be_named";
    // 领取奖励
    public static final String RECEIVE_AWARD = "receive_award";
    // 携带
    public static final String CARRY = "carry";
    // 购买（口粮）
    public static final String BUY_FOOD = "buy_food";
    // 消耗口粮
    public static final String CONSUME_FOOD = "consume_food";

    /* =========== 场景分布式锁key =========== */
    // 发送精华奖励
    public static final String SEND_ESSENCE_BONUS_LOCK = "ump:legendary_neverland:send_essence_bonus_lock:id:%s";
    // 兑换灵兽
    public static final String EXCHANGE_LOCK = "ump:legendary_neverland:exchange_lock:id:%s";
    // 携带/卸下 灵兽
    public static final String CARRY_OR_UNLOADED_LOCK = "ump:legendary_neverland:carry_or_unloaded_lock:id:%s";

    // 任务状态Key[时间+用户+任务]
    public static final String TASK_KEY = "ump:legendary_neverland:task_data:%s:user:%s:task:%s";

    // 所有灵兽缓存
    public static final String ALL_CACHE_SPIRIT_ANIMAL_KEY = "ump:legendary_neverland:cache_all_spirit_animal";
    // 所有口粮缓存
    public static final String ALL_CACHE_RATION_KEY = "ump:legendary_neverland:cache_all_ration";

    // 购买口粮poolCode
    public static final String POOL_CODE_BUY_FOOD = "pool_code_buy_food";

    // 灵兽默认昵称
    public static final String DEFAULT_NICKNAME = "岁岁";
    // 灵兽默认体力
    public static final Long DEFAULT_STAMINA = 0L;
    // 携带灵兽最小体力
    public static final Long CARRY_MIN_STAMINA = 1L;
    // 携带灵兽扣减体力
    public static final Long CARRY_SUB_STAMINA = 1L;

    // 免费口粮list_key
    public static final List<String> FREE_FOOD_KEY_LIST = Arrays.asList("HU_LB", "XI_LH", "SUAN_N", "YU_R");
    // 免费口粮个数
    public static final Long FREE_FOOD_NUM = 1L;

    // 每日体力上限
    public static final Long MAX_STAMINA = 10L;

    // 喜好口粮留存Key
    public static final String LIKE_FOOD_KEY = "ump:legendary_neverland:like_food_key:uid:%s";

    // 每日携带灵兽nx [日期+uid+灵兽Key]
    public static final String CARRY_NX_KEY = "ump:legendary_neverland:carry_nx_key:date:%s:uid:%s:ln_key:%s";



    // 灵兽兑换所需精华
    public static final HashMap<String, Long> SPIRIT_ANIMAL_ESSENCE_MAP = new HashMap<>();
    static {
        SPIRIT_ANIMAL_ESSENCE_MAP.put("DAI_MYL", 200L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("LING_MXL", 300L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("QING_MLL", 400L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("YUN_YLL", 500L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("HUA_YML", 600L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("XUE_WLL", 700L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("WU_YML", 800L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("DIE_WLL", 900L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("YING_LLL", 1000L);
        SPIRIT_ANIMAL_ESSENCE_MAP.put("YUE_HSL", 1200L);
    }

}
