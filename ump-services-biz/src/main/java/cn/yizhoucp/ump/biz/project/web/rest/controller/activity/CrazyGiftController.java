package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeParam;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.ThanksgivingBattleConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.crazygift.CrazyGiftDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.crazygift.CrazyGiftMissionManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class CrazyGiftController {

    @Resource
    private CrazyGiftMissionManager crazyGiftMissionManager;

    @Resource
    private CrazyGiftDrawManager crazyGiftDrawManager;



    @GetMapping("/api/inner/activity/crazy-gift/mission_receive_reward")
    public Result<Boolean> missionReceiveReward(TakePrizeParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> crazyGiftMissionManager.missionReceiveReward(param));
    }

    @GetMapping("/api/inner/activity/crazy-gift/draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> crazyGiftDrawManager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(ThanksgivingBattleConstant.ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }



}
