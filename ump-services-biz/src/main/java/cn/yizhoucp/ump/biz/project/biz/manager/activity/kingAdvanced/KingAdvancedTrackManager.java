package cn.yizhoucp.ump.biz.project.biz.manager.activity.kingAdvanced;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Data
@Service
public class KingAdvancedTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动抽奖
     *
     * @param uid
     * @param poolCode
     * @param awardKey
     * @param awardAmount
     */
    public void allActivityLottery(Long uid, String poolCode, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "advanced _king_chest");
        params.put("pool_code", poolCode);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }


    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(Long uid, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "advanced _king_chest");
        params.put("attribute_type", "platform_activity");
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 所有金币消耗
     */
    public void totalCoinSpend(Long uid){
        Map<String, Object> params = new HashMap<>();
        params.put("from_func", "advanced _king_chest");
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "total_coin_spend", params, ServicesNameEnum.ump_services.getCode());
    }

}
