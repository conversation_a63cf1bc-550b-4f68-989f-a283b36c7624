package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.coinservices.TreasureBoardVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagWall.IndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.cardCollect.CardCollectManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 集卡活动处理
 * @date 2023-06-13 16:15
 */
@RestController
public class CardCollentController {

    @Resource
    CardCollectManager cardCollectManager;

    @RequestMapping("/api/inner/activity/card-collect/index")
    public Result<IndexVO> index(BaseParam param) {
        return Result.successResult(cardCollectManager.getIndex(param));
    }


    @RequestMapping("/api/inner/activity/card-collect/add-gift")
    public Result<Boolean> addGift(BaseParam param,String giftKey,Integer num) {
        return Result.successResult(cardCollectManager.userAddGift(param,giftKey,num));
    }

    @RequestMapping("/api/inner/activity/card-collect/board")
    public Result<TreasureBoardVO> board(BaseParam param) {
        return Result.successResult(cardCollectManager.getBoard(param));
    }

}
