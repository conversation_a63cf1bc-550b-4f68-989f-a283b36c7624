package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre.stragey.ChooseButtonStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre.stragey.PreChineseNewYear2025ButtonStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractButtonManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PreChineseNewYear2025Button extends AbstractButtonManager {

    @Resource
    private ChooseButtonStrategy chooseButtonStrategy;

    @Override
    public Object event(ButtonEventParam buttonEventParam) {
        String buttonCode = buttonEventParam.getEventCode();
        PreChineseNewYear2025ButtonStrategy strategy = chooseButtonStrategy.getStrategy(buttonCode);
        if (strategy == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "未找到对应的按钮");
        }
        return strategy.event(buttonEventParam);
    }
}
