package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.lovepromise;

import cn.yizhoucp.dto.LovePromiseEquityDTO;
import cn.yizhoucp.starter.cdn.enums.CdnEnum;
import cn.yizhoucp.starter.cdn.util.CdnUtil;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "love_promise_equity")
public class LovePromiseEquityDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String icon;

    private String title;

    private String detail;

    public LovePromiseEquityDTO convertToDTO() {
        return LovePromiseEquityDTO.builder()
                .id(this.id)
                .icon(this.icon)
                .title(this.title)
                .detail(this.detail).build();
    }

    public String getIcon() {
        return CdnUtil.getForceUrl(icon, CdnEnum.resCdn);
    }

    public void setIcon(String icon) {
        this.icon = CdnUtil.setUrl(icon);
    }

}
