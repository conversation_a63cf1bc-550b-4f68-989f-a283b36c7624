package cn.yizhoucp.ump.biz.project.biz.remoteService.product;

import cn.yizhoucp.ms.core.base.*;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.productServices.PackageProductVO;
import cn.yizhoucp.product.dto.SendToPackageDTO;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class PackageProductRemoteService {

    @Resource
    private FeignProductService feignProductService;

    @Resource
    private RedisManager redisManager;

    @Resource
    RocketmqProducerManager rocketmqProducerManager;

    private final static String CACHE_PRODUCT_GIFT = "ump:product:gift:%s";

    public List<PackageProductVO> getDetailById(Long appId, String unionId, Long productId) {
        Result<List<PackageProductVO>> result;
        long s = System.currentTimeMillis();
        try {
            result = feignProductService.getDetailById(appId, unionId, productId);
            if (result.success()) {
                log.info("调用 feignProductService.getDetailById 成功 appId:{}, unionId:{}, productId:{}, result:{}, ts:{}", appId, unionId, productId, JSON.toJSONString(result), System.currentTimeMillis() - s);
                return result.successData();
            }
        } catch (Exception e) {
            log.error("调用 feignProductService.getDetailById 异常 appId:{}, unionId:{}, productId:{}", appId, unionId, productId, e);
            return null;
        }
        log.error("调用 feignProductService.getDetailById 失败 appId:{}, unionId:{}, productId:{}, result:{}, ts:{}", appId, unionId, productId, JSON.toJSONString(result), System.currentTimeMillis() - s);
        return null;
    }

    public CoinGiftProductVO getGiftByGiftKey(Long appId, String giftKey) {

        Object obj = redisManager.get(CACHE_PRODUCT_GIFT);
        CoinGiftProductVO coinGiftProductVO;
        if (null == obj) {
            coinGiftProductVO = feignProductService.getGiftByGiftKey(appId, giftKey).successData();
            if (Objects.nonNull(coinGiftProductVO)) {
                redisManager.set(String.format(CACHE_PRODUCT_GIFT, giftKey), coinGiftProductVO, RedisManager.ONE_HOUR_SECONDS);
            } else {
                return null;
            }
        } else {
            coinGiftProductVO = JSON.parseObject(obj.toString(), CoinGiftProductVO.class);
        }

        return coinGiftProductVO;
    }

    /**
     * 下发礼物到背包
     *
     * @param appId         应用id
     * @param uid           用户id
     * @param sendNum       下发数量
     * @param bizId         物品唯一标识
     * @param bizType       类型
     * @param scene         场景
     * @param free          是否免费
     * @param timeLimit     截止时间
     * @param effectiveDay  有效天数
     * @param sendNpcMsg    是否发送小助手消息
     * @param showInPackage 是否在背包中展示
     */
    public void sendGiftToPackage(Long appId, Long uid, Long sendNum, String bizId, String bizType, String scene,
                                  Boolean free, Long timeLimit, Integer effectiveDay, Boolean sendNpcMsg, Boolean showInPackage) {
        if (Objects.isNull(uid) || Objects.isNull(sendNum) || Objects.isNull(bizId) || Objects.isNull(bizType)
                || Objects.isNull(scene)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "下发礼物参数不全");
        }
        SendToPackageDTO param = SendToPackageDTO.builder()
                .appId(Objects.isNull(appId) ? MDCUtil.getCurAppIdByMdc() : appId)
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .uid(uid)
                .sendNum(sendNum)
                .bizId(bizId)
                .bizType(bizType)
                .scene(scene)
                .free(free)
                .timeLimit(timeLimit)
                .effectiveDay(effectiveDay)
                .sendNpcMsg(sendNpcMsg)
                .showInPackage(showInPackage)
                .build();
        rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_USER_PACKET.getTopicKey(), TopicTagEnum.TOPIC_USER_PACKET_ADD.getTagKey(), JSON.toJSONString(param), "send_gift_" + param.getUuid());
    }

    /**
     * 下发礼物到背包
     *
     * @param appId         应用id
     * @param uid           用户id
     * @param sendNum       下发数量
     * @param bizId         物品唯一标识
     * @param bizType       类型
     * @param scene         场景
     * @param free          是否免费
     * @param timeLimit     截止时间
     * @param effectiveDay  有效天数
     * @param sendNpcMsg    是否发送小助手消息
     * @param showInPackage 是否在背包中展示
     */
    public void sendGiftToPackage(Long appId, String unionId, Long uid, Long prizeId, Long sendNum, String bizId, String bizType, String scene,
                                  Boolean free, Long timeLimit, Integer effectiveDay, Boolean sendNpcMsg, Boolean showInPackage, String memo) {
        if (Objects.isNull(uid) || Objects.isNull(sendNum) || Objects.isNull(bizId) || Objects.isNull(bizType)
            || Objects.isNull(scene)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "下发礼物参数不全");
        }
        SendToPackageDTO param = SendToPackageDTO.builder()
                .appId(Objects.isNull(appId) ? MDCUtil.getCurAppIdByMdc() : appId)
                .unionId(StringUtils.isBlank(unionId) ? MDCUtil.getCurUnionIdByMdc() : unionId)
                .uid(uid)
                .userPrizeRecordId(prizeId)
                .sendNum(sendNum)
                .bizId(bizId)
                .bizType(bizType)
                .scene(scene)
                .free(free)
                .timeLimit(timeLimit)
                .effectiveDay(effectiveDay)
                .sendNpcMsg(sendNpcMsg)
                .showInPackage(showInPackage)
                .memo(memo)
                .build();
        rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_USER_PACKET.getTopicKey(), TopicTagEnum.TOPIC_USER_PACKET_ADD.getTagKey(), JSON.toJSONString(param), "send_gift_" + param.getUuid());
    }

    /**
     * 下发礼物到背包
     *
     * @param appId         应用id
     * @param uid           用户id
     * @param sendNum       下发数量
     * @param bizId         物品唯一标识
     * @param bizType       类型
     * @param scene         场景
     * @param free          是否免费
     * @param timeLimit     截止时间
     * @param effectiveDay  有效天数
     * @param sendNpcMsg    是否发送小助手消息
     * @param showInPackage 是否在背包中展示
     * @param addUp         是否累积
     */
    public void sendGiftToPackage(Long appId, String unionId, Long uid, Long prizeId, Long sendNum, String bizId, String bizType, String scene,
                                  Boolean free, Long timeLimit, Integer effectiveDay, Boolean sendNpcMsg, Boolean showInPackage, String memo, Boolean addUp) {
        if (Objects.isNull(uid) || Objects.isNull(sendNum) || Objects.isNull(bizId) || Objects.isNull(bizType)
                || Objects.isNull(scene)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "下发礼物参数不全");
        }
        SendToPackageDTO param = SendToPackageDTO.builder()
                .appId(Objects.isNull(appId) ? MDCUtil.getCurAppIdByMdc() : appId)
                .unionId(StringUtils.isBlank(unionId) ? MDCUtil.getCurUnionIdByMdc() : unionId)
                .uid(uid)
                .userPrizeRecordId(prizeId)
                .sendNum(sendNum)
                .bizId(bizId)
                .bizType(bizType)
                .scene(scene)
                .free(free)
                .timeLimit(timeLimit)
                .effectiveDay(effectiveDay)
                .sendNpcMsg(sendNpcMsg)
                .showInPackage(showInPackage)
                .memo(memo)
                .build();
        rocketmqProducerManager.sendNormalMessage(TopicConstant.TOPIC_USER_PACKET.getTopicKey(), TopicTagEnum.TOPIC_USER_PACKET_ADD.getTagKey(), JSON.toJSONString(param), "send_gift_" + param.getUuid());
    }

}
