package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.OfficialCombineSweetCabinLhDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

/**
 * 官宣甜蜜小屋待领取恋屋币
 *
 * <AUTHOR>
 */
@Repository
public interface OfficialCombineSweetCabinLhJpaDAO extends JpaRepository<OfficialCombineSweetCabinLhDO, Long>, JpaSpecificationExecutor<OfficialCombineSweetCabinLhDO>, CrudRepository<OfficialCombineSweetCabinLhDO, Long> {

    List<OfficialCombineSweetCabinLhDO> findByCabinId(Long cabinId);

    @Transactional
    @Modifying
    @Query(value = "delete from official_combine_sweet_cabin_lh where send_time <= ?1", nativeQuery = true)
    Integer cleanOverTimeList(Long limitTime);

}
