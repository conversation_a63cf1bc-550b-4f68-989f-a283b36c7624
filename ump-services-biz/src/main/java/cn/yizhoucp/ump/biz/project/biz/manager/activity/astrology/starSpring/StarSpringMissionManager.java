package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSpring;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeParam;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeReturn;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractMissionTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.MissionContext;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.AstrologyValueManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.TASK_1_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.TASK_1_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.TASK_1_TAKE_PRIZE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.TASK_TAKE_PRIZE;

@Service
@Slf4j
public class StarSpringMissionManager extends AbstractMissionTemplate {

    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private AstrologyValueManager astrologyValueManager;
    @Resource
    private StarSpringTrackManager starSpringTrackManager;

    @Override
    @ActivityCheck(activityCode = ACTIVITY_CODE)
    public TakePrizeReturn takePrize(TakePrizeParam param) {
        if (StringUtils.isBlank(param.getTaskCode())) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        if (TASK_1_CODE.equals(param.getTaskCode())) {
            Integer task1CurFinishTimes = Optional.ofNullable(redisManager.getInteger(String.format(TASK_1_CUR_FINISH_TIMES, param.getUid(), DateUtil.getNowYyyyMMdd()))).orElse(0);
            if (1 > task1CurFinishTimes) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "您尚未完成任务，快去完成任务吧～");
            }

            if (!Boolean.TRUE.equals(redisManager.setnx(String.format(TASK_1_TAKE_PRIZE, param.getUid(), DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "今日您已获得10星海值，明天再来看看吧～");
            }

            astrologyValueManager.increase(param.getUid(), 10L, "蛇舞新春-任务一");

            starSpringTrackManager.allActivityReceiveAward(TASK_1_CODE, null, null, null, null, param.getUid());

            return TakePrizeReturn.builder().build();
        }

        for (StarSpringConstant.Task task : StarSpringConstant.Task.values()) {
            if (task.name().equals(param.getTaskCode())) {
                Integer taskCurFinishTimes = Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), task.name()))).orElse(0);
                if (task.getItemNum() > taskCurFinishTimes) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您尚未完成该任务，继续完成吧～");
                }

                if (!Boolean.TRUE.equals(redisManager.setnx(String.format(TASK_TAKE_PRIZE, param.getUid(), task.name()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您已完成该任务，看看其他任务吧～");
                }

                List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, param.getTaskCode());
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                );

                starSpringTrackManager.allActivityReceiveAward(task.name(), null, scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), param.getUid());

                return TakePrizeReturn.builder().prizeItemList(this.scenePrizeDO2PrizeItem(scenePrizeDOList)).build();
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    protected Boolean doCheck(MissionContext context) {
        return null;
    }

    @Override
    protected Boolean init(MissionContext context) {
        return null;
    }

    @Override
    protected Boolean completedProcess(MissionContext context) {
        return null;
    }

    private List<PrizeItem> scenePrizeDO2PrizeItem(List<ScenePrizeDO> params) {
        List<PrizeItem> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(params)) {
            for (ScenePrizeDO item : params) {
                list.add(PrizeItem.builder()
                        .valueGold(Math.toIntExact(item.getPrizeValueGold()))
                        .prizeIcon(item.getPrizeIcon())
                        .prizeName(item.getPrizeDesc()).build());
            }
        }
        return list;
    }

}
