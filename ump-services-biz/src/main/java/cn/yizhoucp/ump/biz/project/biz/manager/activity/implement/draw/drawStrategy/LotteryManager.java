package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.drawStrategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.DrawPooExtractType;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DoDrawDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.KeyWeight;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LotteryManager {

    @Resource
    private BigSmallPoolLotteryStrategy bigSmallPoolLotteryStrategy;

    @Resource
    private ProbabilityLotteryStrategy probabilityLotteryStrategy;

    public void clearPool(DrawPooExtractType poolType, String poolCode) {
        if (DrawPooExtractType.pool_everyone_only.equals(poolType) || DrawPooExtractType.pool.equals(poolType)) {
            bigSmallPoolLotteryStrategy.clearPool(poolCode);
        }
    }

    public List<String> draw(DrawPooExtractType poolType, DoDrawDTO doDrawDTO) {
        log.info("抽奖 -> {}, {}", poolType, doDrawDTO);
        String poolCode = doDrawDTO.getPoolCode();
        Integer drawTimes = doDrawDTO.getDrawTimes();
        List<KeyWeight> originPool = doDrawDTO.getOriginPool();

        if (DrawPooExtractType.pool.equals(poolType)) {
            return this.drawWithPublicPool(drawTimes, poolCode, originPool);
        }

        if (DrawPooExtractType.random.equals(poolType)) {
            return this.drawWithProbability(drawTimes, poolCode, originPool);
        }

        if (DrawPooExtractType.pool_everyone_only.equals(poolType)) {
            Long uid = doDrawDTO.getUid();
            if (uid == null) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
            return this.drawWithPrivatePool(drawTimes, poolCode, uid, originPool);
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM, "奖池类型不支持");
    }

    private List<String> drawWithPrivatePool(Integer drawTimes, String poolCode, Long uid, List<KeyWeight> originPool) {
        return bigSmallPoolLotteryStrategy.drawByBigPool(poolCode, drawTimes, uid, originPool);
    }

    private List<String> drawWithProbability(Integer drawTimes, String poolCode, List<KeyWeight> originPool) {
        return probabilityLotteryStrategy.drawTimes(drawTimes, poolCode, originPool);
    }

    private List<String> drawWithPublicPool(Integer drawTimes, String poolCode, List<KeyWeight> originPool) {
        return bigSmallPoolLotteryStrategy.drawBySmallPool(poolCode, 5000 ,drawTimes, originPool);
    }
}
