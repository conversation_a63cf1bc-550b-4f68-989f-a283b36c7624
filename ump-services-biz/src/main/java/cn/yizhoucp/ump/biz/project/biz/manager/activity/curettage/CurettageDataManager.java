package cn.yizhoucp.ump.biz.project.biz.manager.activity.curettage;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class CurettageDataManager {
    @Resource
    private RedisManager redisManager;

    @Resource
    private CurettageDateHelpManager helper;

    public static final Long REDIS_MAX_TIME = RedisManager.ONE_DAY_SECONDS*15;

    // -- 参与占星的用户 --
    public void addDrawPerson(Long uid) {
        redisManager.sSetExpire(getDrawPersonKey(), REDIS_MAX_TIME, uid);
    }

    public boolean personDraw(Long uid) {
        return redisManager.setIsMember(getDrawPersonKey(), uid);
    }

    private String getDrawPersonKey() {
        return "ump:curettage2:alldrawperson";
    }

    // -- 打开铂金宝箱的用户 --
    public void addHasAppendTask(Long uid) {
        redisManager.sSetExpire(getHasAppendTaskKey(), REDIS_MAX_TIME, uid);
    }

    public boolean personHasAppendTask(Long uid) {
        return redisManager.setIsMember(getHasAppendTaskKey(), uid);
    }

    private String getHasAppendTaskKey() {
        return "ump:curettage2:hasappendtask";
    }



    // -- 用户今天刮奖池累计 --
    public void addDrawCount(Long uid, Integer consumeTimes) {
        redisManager.incrLong(getPersonTodayDrawKey(uid), consumeTimes, REDIS_MAX_TIME);
    }

    public Long getTodayDrawTimes(Long uid) {
        return Optional.ofNullable(redisManager.getLong(getPersonTodayDrawKey(uid))).orElse(0L);
    }

    private String getPersonTodayDrawKey(Long uid) {
        return String.format("ump:curettage2:persontodaydarwtimes:%s:%s", helper.getTodayIndex(), uid);
    }


    // -- 全服所有用户开追加宝箱次数 --

    public Integer incrAllOpenBoxTimes(Integer box) {
        return redisManager.incrLong(getAllOpenBoxTimesKey(box), 1, REDIS_MAX_TIME).intValue();
    }

    public Integer getAllOpenAppendBoxTimes(Integer box) {
        return Optional.ofNullable(redisManager.getInteger(getAllOpenBoxTimesKey(box))).orElse(0);
    }

    private String getAllOpenBoxTimesKey(Integer box) {
        return String.format("ump:curettage2:all:openbox:%s", box);
    }


    // -- 用户今天刮奖某个宝箱的次数 --

    public Integer personTodayOpenTimes(Long uid, Integer box) {
        Object hget = redisManager.hget(getPersonTodayOpenBoxTimesKey(uid), String.valueOf(box));
        if (hget == null) {
            return 0;
        }
        return Integer.valueOf(String.valueOf(hget));
    }

    private String getPersonTodayOpenBoxTimesKey(Long uid) {
        return String.format("ump:curettage2:personopenboxtimes:%s:%s", helper.getTodayIndex(), uid);
    }


    // -- 用户活动期间刮奖的次数 --

    public void afterOpenBox(Long uid, Integer box) {
        redisManager.hincr(getPersonTodayOpenBoxTimesKey(uid), String.valueOf(box), 1, REDIS_MAX_TIME);
        redisManager.hincr(getPersonAllOpenBoxTimesKey(uid), String.valueOf(box), 1, REDIS_MAX_TIME);
        if (box == 2) {
            addHasAppendTask(uid);
        }
    }

    public Integer getPersonAllOpenBoxTimes(Long uid, Integer box) {
        return Optional.ofNullable(redisManager.hget(getPersonAllOpenBoxTimesKey(uid), String.valueOf(box)))
                .map(x -> Integer.parseInt(String.valueOf(x)))
                .orElse(0);
    }

    private String getPersonAllOpenBoxTimesKey(Long uid) {
        return String.format("ump:curettage2:allopenboxtimes:%s", uid);
    }

    // -- 用户打开追加宝箱次数 --

    public Integer getPersonOpenAppendBoxTimes(Long uid, Integer box) {
        Object hget = redisManager.hget(getPersonOpenAppendBoxKey(uid), String.valueOf(box));
        if (hget == null) {
            return 0;
        }
        return Integer.valueOf(String.valueOf(hget));
    }

    public void afterOpenAppendBox(Long uid, Integer box) {
        redisManager.hincr(getPersonOpenAppendBoxKey(uid), String.valueOf(box), 1, REDIS_MAX_TIME);
    }

    private String getPersonOpenAppendBoxKey(Long uid) {
        return String.format("ump:curettage2:personopenappendbox:%s", uid);
    }

    // -- 冲刺奖励相关 --

    public Long getTodayChangeTimes() {
        return Optional.ofNullable(redisManager.getLong(getTodayChangeTimesKey())).orElse(0L);
    }

    public Long incrTodayChangeTimes(Long times) {
        return redisManager.incrLong(getTodayChangeTimesKey(), times, REDIS_MAX_TIME);
    }

    private String getTodayChangeTimesKey() {
        return String.format("ump:curettage2:todaychangetimes:%s", helper.getTodayIndex());
    }

    public Long getTodaySuperChangeTimes() {
        return Optional.ofNullable(redisManager.getLong(getTodaySuperChangeTimesKey())).orElse(0L);
    }

    public Long incrTodaySuperChangeTimes(Long times) {
        return redisManager.incrLong(getTodaySuperChangeTimesKey(), times, REDIS_MAX_TIME);
    }

    private String getTodaySuperChangeTimesKey() {
        return String.format("ump:curettage2:todaysuperchangetimes:%s", helper.getTodayIndex());
    }

    public void recordReplaceNormal(Integer index) {
        redisManager.set(getNormalReplaceRecordKey(index), System.currentTimeMillis(), REDIS_MAX_TIME);
    }

    public void recordReplaceSuper(Integer index) {
        redisManager.set(getSuperReplaceRecordKey(index), System.currentTimeMillis(), REDIS_MAX_TIME);
    }

    public boolean isReplaceNormal(Integer index) {
        return redisManager.hasKey(getNormalReplaceRecordKey(index));
    }

    public boolean isReplaceSuper(Integer index) {
        return redisManager.hasKey(getSuperReplaceRecordKey(index));
    }

    private String getNormalReplaceRecordKey(Integer index) {
        return String.format("ump:curettage2:normalreplacerecord:%s", index);
    }

    private String getSuperReplaceRecordKey(Integer index) {
        return String.format("ump:curettage2:superreplacerecord:%s", index);
    }

    public void setReplaceGift(Long valueGold, PrizeItem prizeItem) {
        redisManager.hset(getReplaceKey(), String.valueOf(valueGold), JSON.toJSON(prizeItem), REDIS_MAX_TIME);
    }
    public Map<Long, PrizeItem> getReplaceGiftMap() {
        Map<Long, PrizeItem> result = new HashMap<>();
        Map<Object, Object> hmget = redisManager.hmget(getReplaceKey());
        for (Map.Entry<Object, Object> objectObjectEntry : hmget.entrySet()) {
            result.put(Long.parseLong(String.valueOf(objectObjectEntry.getKey())), JSON.parseObject(String.valueOf(objectObjectEntry.getValue()), PrizeItem.class));
        }
        return result;
    }

    private String getReplaceKey() {
        return "ump:curettage2:replacegift";
    }

}
