package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.luckyCharm.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.luckyCharm.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyCharm.LuckyCharmPageManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RequestMapping("/api/inner/activity/lucky-charm")
@RestController
public class LuckyCharmController {

    @Resource
    private LuckyCharmPageManager luckyCharmPageManager;

    @GetMapping("/get-index")
    public Result<IndexVO> getIndex(BaseParam param) {
        return Result.successResult(luckyCharmPageManager.getPageInfo(param));
    }

    @GetMapping("/get-rank")
    public Result<RankVO> getRank(BaseParam param) {
        return Result.successResult(luckyCharmPageManager.getRankInfo(param));
    }

}
