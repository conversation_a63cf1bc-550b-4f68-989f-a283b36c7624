package cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyCharm;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ms.core.vo.userservices.userPackage.BatchUsePackageVO;
import cn.yizhoucp.ms.core.vo.userservices.userPackage.UsePackageDetailVO;
import cn.yizhoucp.product.client.UserPackageFeignService;
import cn.yizhoucp.product.dto.UsePackageDTO;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList.DefaultBarrageManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.UserRemoteService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.userAccount.UserAccountFeignService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.yizhoucp.ms.core.base.ErrorCode.MISS_PARAM;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyCharmConstant.*;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyCharmConstant.LuckyCharm.goldCharm;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LuckyCharmConstant.LuckyCharm.redCharm;

/**
 * <AUTHOR>
 * @Date 2023/1/11 16:34
 * @Version 1.0
 */
@Slf4j
@Component
public class LuckyCharmDrawManager extends AbstractDrawTemplate {

    @Resource
    private LuckyCharmBizManager luckyCharmBizManager;
    @Resource
    private UserPackageFeignService userPackageFeignService;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private DefaultBarrageManager defaultBarrageManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisManager redisManager;

    /**
     * 资源检查
     *
     * @param context
     * @return
     */
    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        String bizId;
        long count;
        // 根据奖池信息扣除资源
        if (StringUtils.equalsIgnoreCase(DrawPoolType.redCharm.name(), drawParam.getPoolCode())) {
            bizId = redCharm.getKey();
            count = 8L;
        } else if (StringUtils.equalsIgnoreCase(DrawPoolType.goldCharm.name(), drawParam.getPoolCode())) {
            bizId = goldCharm.getKey();
            count = 1L;
        } else {
            throw new ServiceException(MISS_PARAM, "poolCode 非法");
        }

        // 初次抽奖 不扣减资源
        if (!isFirstDrawRedCharmPool(drawParam)) {
            // 扣除资源
            List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithBizIdAndType(drawParam.getAppId(), drawParam.getUnionId(), drawParam.getUid(),
                    bizId, UserPackageBizType.GIFT.getCode(), count, PackageUseScene.activity.getCode());
            if (CollectionUtils.isEmpty(usePackageList)) {
                throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180020, "锦鲤不足，快来做任务吧～");
            }
            log.info("luckyCharm 资源扣减 uid {} poolCode {} bizId {} count {}", drawParam.getUid(), drawParam.getPoolCode(), bizId, count);
        }
    }

    /**
     * 抽奖实现
     *
     * @param context
     * @return
     */
    @Override
    protected void draw(DrawContext context) {
        log.info("luckycharm 2023 context {}", JSON.toJSONString(context));
        DrawParam param = context.getDrawParam();

        // 获取奖池属性
        DrawPoolDO drawPool = context.getDrawPoolDO();
        context.setDrawPoolDO(drawPool);

        // 获取本轮抽中奖品
        List<DrawPoolItemDTO> prizeItems = probStrategy.getDrawPoolItems(context);

        // 首次在 红锦鲤奖池中 必得 头像框
        if (isFirstDrawRedCharmPool(param)) {
            prizeItems = getDefaultPrize(drawPool.getPoolCode(), false);
            markFirstDrawRedCharmPool(param);
        }

        // 基金处理
        if (Boolean.FALSE.equals(luckyCharmBizManager.checkBonusIsEnough(param.getUid(),
                prizeItems.get(0).getDrawPoolItemDO().getItemValueGold()))) {
            prizeItems = getDefaultPrize(drawPool.getPoolCode(), false);
            log.info("luckyCharm 基金不足 uid {}", param.getUid());
        } else {
            luckyCharmBizManager.decrBonus(param.getUid(), prizeItems.get(0).getDrawPoolItemDO().getItemValueGold());
            log.info("luckyCharm 扣除基金 uid {} cost {}", param.getUid(), prizeItems.get(0).getDrawPoolItemDO().getItemValueGold());
        }

        // 天选之人
        if (Boolean.TRUE.equals(chosen(param.getBaseParam())) && Boolean.TRUE.equals(deductionStock())) {
            log.info("luckyCharm 天选之人下发指定奖品 prize {}", JSON.toJSONString(REAL_PRIZE));
            prizeItems = Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(REAL_PRIZE).build());
            param.setNoSendPrize(Boolean.TRUE);
        }

        // 天选之人 凤凰来仪礼物
        if (StringUtils.equalsIgnoreCase(context.getDrawParam().getPoolCode(), "redCharm") &&
                Boolean.TRUE.equals(chosenFhly(param.getBaseParam())) &&
                Boolean.TRUE.equals(deductionStockFhly())) {
            log.info("luckyCharm 天选之人下发指定奖品 prize {}", JSON.toJSONString(FHLY_PRIZE));
            prizeItems = Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(FHLY_PRIZE).build());
        }

        log.info("luckyCharm 中奖结果 uid {} prize {}", param.getUid(), JSON.toJSONString(prizeItems));
        context.setPrizeItemList(prizeItems);

    }

    /**
     * 资源扣除
     *
     * @param context
     * @return
     */
    @Override
    protected void deductResource(DrawContext context) {

    }

    /**
     * 后置处理
     *
     * @param context
     * @return
     */
    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        UserBaseVO user = userRemoteService.getBasicAll(drawParam.getAppId(), drawParam.getUid(), Boolean.TRUE);
        if (Objects.isNull(user)) {
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION);
        }
        // 获取奖品信息
        DrawPoolItemDO prize = context.getPrizeItemList().get(0).getDrawPoolItemDO();
        // 添加弹幕
        defaultBarrageManager.putBarrage(String.format("恭喜 %s 在锦鲤转盘中获得 %s", getName(user.getName()), prize.getItemName()));
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());
        notifyUser(context);
    }

    private void notifyUser(DrawContext context) {
        DrawPoolItemDO prize = context.getPrizeItemList().get(0).getDrawPoolItemDO();
        String msg = "";
        if (StringUtils.equalsIgnoreCase(prize.getItemKey(), "dress_up")) {
            msg = String.format("恭喜您在“做任务 抢锦鲤”活动中抽取到“%s”，可进入我的-我的装扮可以佩戴～", prize.getItemName());
        } else if (StringUtils.equalsIgnoreCase(prize.getItemKey(), "SQFD_EXPERIENCE_COUPON")) {
            msg = "恭喜您在“做任务 抢锦鲤”活动中抽取到“福袋券”，进入福袋可免费抽取哦～";
        } else if (StringUtils.equalsIgnoreCase(prize.getItemKey(), "HJYGTDZ_PRIZE")) {
            msg = "恭喜您在“做任务 抢锦鲤”中抽到黄金月桂兔吊坠！！！请添加客服微信：yizhoujun63提供收货地址领取奖品～";
        } else {
            msg = String.format("恭喜您在“做任务 抢锦鲤”活动中抽取到“%s”，快送给心爱的“TA”吧～", prize.getItemName());
        }
        if (StringUtils.isNotBlank(msg)) {
            notifyComponent.npcNotify(context.getDrawParam().getUid(), msg);
        }
    }

    private Boolean chosen(BaseParam param) {
        String key = String.format(CHOSEN_ONE, param.getUid());
        if (redisManager.hasKey(key)) {
            redisManager.delete(key);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean chosenFhly(BaseParam param) {
        String key = String.format(CHOSEN_FHLY, param.getUid());
        if (redisManager.hasKey(key)) {
            redisManager.delete(key);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean deductionStock() {
        Integer val = (Integer) redisManager.get(REAL_GIFT_STOCK);
        if (Objects.nonNull(val) && val > 0) {
            redisManager.decrLong(REAL_GIFT_STOCK, 1, DateUtil.ONE_MONTH_SECOND);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean deductionStockFhly() {
        Integer val = (Integer) redisManager.get(FHLY_STOCK);
        if (Objects.nonNull(val) && val > 0) {
            redisManager.decrLong(FHLY_STOCK, 1, DateUtil.ONE_MONTH_SECOND);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean isFirstDrawRedCharmPool(DrawParam param) {
        return !redisManager.setIsMember(LUCKYCHARM_MARK, param.getUid()) &&
                StringUtils.equalsIgnoreCase(param.getPoolCode(), DrawPoolType.redCharm.name());
    }

    private void markFirstDrawRedCharmPool(DrawParam drawParam) {
        if (StringUtils.equalsIgnoreCase(drawParam.getPoolCode(), DrawPoolType.redCharm.name())) {
            redisManager.sSetExpire(LUCKYCHARM_MARK, DateUtil.ONE_MONTH_SECOND, drawParam.getUid());
        }
    }

    /**
     * 凤凰来仪 礼物需要特殊处理
     *
     * @param prizeItems
     */
    private List<DrawPoolItemDTO> filterLimitedGifts(List<DrawPoolItemDTO> prizeItems) {
        log.debug("debug1-1 prize:{}", JSON.toJSONString(prizeItems));
        DrawPoolItemDTO drawPoolItemDTO = prizeItems.get(0);
        if (StringUtils.equalsIgnoreCase(LIMIT_GIFT_KEY, drawPoolItemDTO.getDrawPoolItemDO().getItemKey())) {
            log.debug("debug1-2");
            RLock lock = redissonClient.getLock(FHLY_STOCK_LOCK);
            try {
                lock.lock();
                // 当天已经被抽中过
                if (redisManager.hasKey(String.format(ISSUED_FHLY_MARKS, DateUtil.getNowYyyyMMdd()))) {
                    log.debug("debug1-3");
                    return getDefaultPrize(drawPoolItemDTO.getDrawPoolItemDO().getPoolCode(), true);
                }
                log.debug("debug2-1");
                // 1、查库存
                Long stock = Optional.ofNullable((Integer) redisManager.get(FHLY_STOCK)).orElse(0).longValue();
                log.debug("debug2-2 stock:{}", stock);
                // 2、 减库存
                if (stock > 0) {
                    log.debug("debug2-3");
                    redisManager.decrLong(FHLY_STOCK, 1, DateUtil.ONE_MONTH_SECOND);
                    // 3、标记抽取记录
                    redisManager.set(String.format(ISSUED_FHLY_MARKS, DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
                } else {
                    log.info("库存不足 下发头像框");
                    return getDefaultPrize(drawPoolItemDTO.getDrawPoolItemDO().getPoolCode(), true);
                }
            } finally {
                lock.unlock();
            }
        }
        return prizeItems;
    }

    private List<DrawPoolItemDTO> getDefaultPrize(String poolCode, Boolean bonusIsEnough) {
        if (!bonusIsEnough) {
            return Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_CHARM_PRIZE).build());
        }
        if (StringUtils.equalsIgnoreCase(poolCode, DrawPoolType.redCharm.name())) {
            return Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_RED_CHARM_PRIZE).build());
        } else if (StringUtils.equalsIgnoreCase(poolCode, DrawPoolType.goldCharm.name())) {
            return Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_GOLD_CHARM_PRIZE).build());
        } else {
            return Lists.newArrayList(DrawPoolItemDTO.builder().targetTimes(1).drawPoolItemDO(DEFAULT_CHARM_PRIZE).build());
        }
    }

    private String getName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() < 2) {
            return name;
        } else if (name.length() == 2) {
            return name.charAt(0) + "*";
        } else {
            return name.charAt(0) + "*" + name.charAt(name.length() - 1);
        }
    }


}
