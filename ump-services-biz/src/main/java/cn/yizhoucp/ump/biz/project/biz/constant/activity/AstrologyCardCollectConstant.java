package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 占星集卡
 * @date 2023-07-20 16:20
 */
public class AstrologyCardCollectConstant {

    /** 用户占星墙 redis-hash（uid） */
    public static final String USER_WALL = "ump:astrologyCardCollect:%s";
    /** 礼物墙 */
    public static final List<PrizeItem> prizeWallTemplate = Lists.newArrayList(
            PrizeItem.builder().prizeName("爪爪雪糕").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689134614910120.png").prizeKey("ZZXG_GIFT_ASTROLOGY").valueGold(10).prizeNum(0).build(),
            PrizeItem.builder().prizeName("萌爪爪").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689129978456523.png").prizeKey("MZZ_GIFT_ASTROLOGY").valueGold(20).prizeNum(0).build(),
            PrizeItem.builder().prizeName("心动甜饮").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689132182579418.png").prizeKey("XDTY_GIFT_ASTROLOGY").valueGold(52).prizeNum(0).build(),
            PrizeItem.builder().prizeName("泡泡相机").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689127889327454.png").prizeKey("PPXJ_GIFT_ASTROLOGY").valueGold(188).prizeNum(0).build(),
            PrizeItem.builder().prizeName("富士春樱").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689131224178444.png").prizeKey("FSCY_GIFT_ASTROLOGY").valueGold(520).prizeNum(0).build(),
            PrizeItem.builder().prizeName("求爱岛").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689132068588787.png").prizeKey("QAD_GIFT_ASTROLOGY").valueGold(888).prizeNum(0).build(),
            PrizeItem.builder().prizeName("初见倾心").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689131072204607.png").prizeKey("CJQX_GIFT_ASTROLOGY").valueGold(1314).prizeNum(0).build(),
            PrizeItem.builder().prizeName("衣香鬓影").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689132343016635.png").prizeKey("YXBY_GIFT_ASTROLOGY").valueGold(3344).prizeNum(0).build(),
            PrizeItem.builder().prizeName("赛博情愫").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689131712738297.png").prizeKey("SBQS_GIFT_ASTROLOGY").valueGold(5200).prizeNum(0).build(),
            PrizeItem.builder().prizeName("此生予你").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689127813408364.png").prizeKey("CSYN_GIFT_ASTROLOGY").valueGold(9999).prizeNum(0).build()
    );
    /** 奖品信息 */
    public static final PrizeItem tradePrizeTemplate = PrizeItem.builder().prizeName("鲸落星海").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-07/1689131913647661.png").prizeKey("JLCH_GIFT_ASTROLOGY").valueGold(19999).prizeNum(0).build();

    /** 兑换锁 redis（uid） */
    public static final String TRADE_LOCK = "ump:AstrologyCardCollect:tradeLock_%s";

    /** 集卡活动榜单 redis-zSet*/
    public static final String CARD_COLLECT_BOARD = "ump:astrology_card_collect_board";
    public static final long MAX_TIMESTAMP = 9999999999999L;
}
