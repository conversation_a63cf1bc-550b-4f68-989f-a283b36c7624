package cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public class StarSpringConstant {

    public static final String ACTIVITY_CODE = "star_spring";

    /** uid yyyyMMdd */
    public static final String ASTROLOGY_TIMES = "ump:star_spring:astrology_times:%s:%s";

    /** uid yyyyMMdd RedEnvelope.name() */
    public static final String RED_ENVELOPE_OPEN = "ump:star_spring:red_envelope_open:%s:%s:%s";

    /** yyyyMMdd */
    public static final String DAY_RANK = "ump:star_spring:day_rank:%s";
    public static final String OVERALL_RANK = "ump:star_spring:overall_rank";

    public static final String TASK_1_CODE = "TASK_1";
    /** uid yyyyMMdd */
    public static final String TASK_1_CUR_FINISH_TIMES = "ump:star_spring:task_1_cur_finish_times:%s:%s";
    /** uid yyyyMMdd */
    public static final String TASK_1_TAKE_PRIZE = "ump:star_spring:task_1_take_prize:%s:%s";
    /** uid Task.name() */
    public static final String TASK_CUR_FINISH_TIMES = "ump:star_spring:task_cur_finish_times:%s:%s";
    /** uid Task.name() */
    public static final String TASK_TAKE_PRIZE = "ump:star_spring:task_take_prize:%s:%s";
    /** CountryTask.name() */
    public static final String COUNTRY_TASK_CUR_FINISH_TIMES = "ump:star_spring:country_task_cur_finish_times:%s";

    public static final String REPLACE_GIFT = "ump:star_spring:replace_gift";

    @AllArgsConstructor
    @Getter
    public enum RedEnvelope {
        RED_ENVELOPE_1(10),
        RED_ENVELOPE_2(129),
        RED_ENVELOPE_3(299),
        RED_ENVELOPE_4(1688),
        RED_ENVELOPE_5(3344),
        RED_ENVELOPE_6(8888),
        ;

        private final int astrologyTimes;
    }

    @AllArgsConstructor
    @Getter
    public enum Task {
        TASK_2(Arrays.asList(52L, 188L), 88),
        TASK_3(Arrays.asList(520L, 1314L), 66),
        TASK_4(Arrays.asList(3344L, 5200L), 10),
        TASK_5(Arrays.asList(9999L, 19999L), 6),
        ;

        private final List<Long> itemValueGold;
        private final int itemNum;
    }

    @AllArgsConstructor
    @Getter
    public enum CountryTask {
        COUNTRY_TASK_1("SJQ_GIFT_ASTROLOGY", "astrology_times", 15000, null),
        COUNTRY_TASK_2("SXXB_GIFT_ASTROLOGY", "astrology_times", 20000, null),
        COUNTRY_TASK_3("QYMG_GIFT_ASTROLOGY", "astrology_times", 25000, null),
        COUNTRY_TASK_4("DRLM_GIFT_ASTROLOGY", "astrology_times", 35000, null),
        COUNTRY_TASK_5("WYGH_GIFT_ASTROLOGY", "gift_count", 10, 5200L),
        COUNTRY_TASK_6("BYZL_GIFT_ASTROLOGY", "gift_count", 5, 9999L),
        COUNTRY_TASK_7("HHPD_GIFT_ASTROLOGY", "gift_count", 3, 19999L),
        COUNTRY_TASK_8("XYQZ_GIFT_ASTROLOGY", "astrology_times", 15000, null),
        COUNTRY_TASK_9("DJCA_GIFT_ASTROLOGY", "astrology_times", 20000, null),
        COUNTRY_TASK_10("YYLM_GIFT_ASTROLOGY", "astrology_times", 25000, null),
        COUNTRY_TASK_11("DSYH_GIFT_ASTROLOGY", "astrology_times", 35000, null),
        COUNTRY_TASK_12("YLXJ_GIFT_ASTROLOGY", "gift_count", 10, 5200L),
        COUNTRY_TASK_13("LMDKZJ_GIFT_ASTROLOGY", "gift_count", 5, 9999L),
        COUNTRY_TASK_14("PDZY_GIFT_ASTROLOGY", "gift_count", 3, 19999L),
        ;

        private final String prizeKey;
        private final String type;
        private final int targetValue;
        private final Long giftCoin;
    }

}
