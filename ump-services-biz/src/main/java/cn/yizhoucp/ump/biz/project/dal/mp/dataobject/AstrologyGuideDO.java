package cn.yizhoucp.ump.biz.project.dal.mp.dataobject;


import cn.yizhoucp.enums.AstrologyGuideStatusEnum;
import cn.yizhoucp.ump.api.vo.AstrologyGuideVO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("astrology_guide")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AstrologyGuideDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long uid;

    /** {@link AstrologyGuideStatusEnum} */
    @TableField("`status`")
    private String status;

    public AstrologyGuideVO convertToVO() {
        return AstrologyGuideVO.builder()
                .status(status)
                .build();
    }
}
