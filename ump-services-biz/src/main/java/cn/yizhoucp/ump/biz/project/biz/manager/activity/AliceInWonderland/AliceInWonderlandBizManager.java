package cn.yizhoucp.ump.biz.project.biz.manager.activity.AliceInWonderland;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.dto.sns.UserIntimacyDTO;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.aliceInWonderland.*;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.AliceInWonderlandConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.ump.biz.project.web.param.activity.aliceInWondland.AwardRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AliceInWonderlandBizManager implements ActivityComponent {

    @Resource
    private RedisManager redisManager;

    @Resource
    private FeignUserService feignUserService;

    @Resource
    private DrawPoolItemJpaDAO drawPoolItemJpaDAO;

    @Resource
    private DepthFeignService depthFeignService;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private ScenePrizeService scenePrizeService;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private FeignSnsService feignSnsService;

    @Resource
    private AliceInWonderlandRankManager aliceInWonderlandRankManager;

    @Resource
    private AliceInWonderlandTrackManager aliceInWonderlandTrackManager;



    @ActivityCheck(activityCode = AliceInWonderlandConstant.ACTIVITY_CODE,isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList){
        Long appId = param.getAppId();
        for(CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList){
            Long uid=coinGiftGivedModel.getFromUid();
            Long toUid = coinGiftGivedModel.getToUid();
            List<Long> uidList=Arrays.asList(uid,toUid);
            //2.礼物墙收集
/*
            if(AliceInWonderlandConstant.WALL_GIFT_KEY.contains(coinGiftGivedModel.getGiftKey())){
*/
            if(AliceInWonderlandConstant.GiftWall.containGiftKey(coinGiftGivedModel.getGiftKey())){
                String collectedKey = AliceInWonderlandConstant.createAliceGiftWallCollectedRedisKey(uid,coinGiftGivedModel.getGiftKey());
                redisManager.incrLong(collectedKey, coinGiftGivedModel.getProductCount(), cn.yizhoucp.ms.core.base.util.DateUtil.ONE_MONTH_SECOND);
                continue;
            }
            //3.阵营榜单
            if (AliceInWonderlandConstant.LLHSLH_BOX_ITEMS.contains(coinGiftGivedModel.getGiftKey()) ||
                    AliceInWonderlandConstant.AXBDLH_BOX_ITEMS.contains(coinGiftGivedModel.getGiftKey()) ||
                    AliceInWonderlandConstant.TMYCLH_BOX_ITEMS.contains(coinGiftGivedModel.getGiftKey())) {
                Long coin;
                if(AliceInWonderlandConstant.TMYCLH_BOX_ITEMS.contains(coinGiftGivedModel.getGiftKey())){
                    coin = 5200L;
                    for(Long id : uidList){
                        String taskKey=AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(id,AliceInWonderlandConstant.GreenPathTask.GREEN_PATH_TASK_5.getTaskCode(),DateUtil.getNowYyyyMMdd());
                        Long taskValue =redisManager.incrLong(taskKey,coinGiftGivedModel.getProductCount(),DateUtil.ONE_MONTH_SECOND);
                        log.info("ALiceInWonderlandBizManager#sendGiftHandler task_5 taskValue {}",taskValue);
                        String collectedTaskKey=AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(id,AliceInWonderlandConstant.GreenPathTask.GREEN_PATH_TASK_6.getTaskCode(),DateUtil.getNowYyyyMMdd());
                        redisManager.sSetExpire(collectedTaskKey,DateUtil.ONE_MONTH_SECOND,coinGiftGivedModel.getGiftKey());
                    }
                }else if(AliceInWonderlandConstant.AXBDLH_BOX_ITEMS.contains(coinGiftGivedModel.getGiftKey())){
                    coin = 1314L;
                    for(Long id : uidList){
                        String taskKey=AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(id,AliceInWonderlandConstant.GreenPathTask.GREEN_PATH_TASK_3.getTaskCode(),DateUtil.getNowYyyyMMdd());
                        Long taskValue = redisManager.incrLong(taskKey, coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
                        log.info("ALiceInWonderlandBizManager#sendGiftHandler task_3 taskValue {}",taskValue);
                        String collectedTaskKey=AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(id,AliceInWonderlandConstant.GreenPathTask.GREEN_PATH_TASK_4.getTaskCode(),DateUtil.getNowYyyyMMdd());
                        redisManager.sSetExpire(collectedTaskKey,DateUtil.ONE_MONTH_SECOND,coinGiftGivedModel.getGiftKey());
                    }
                }else{
                    coin = 52L;
                    for(Long id : uidList){
                        String taskKey=AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(id,AliceInWonderlandConstant.GreenPathTask.GREEN_PATH_TASK_1.getTaskCode(),DateUtil.getNowYyyyMMdd());
                        Long taskValue = redisManager.incrLong(taskKey, coinGiftGivedModel.getProductCount(), DateUtil.ONE_MONTH_SECOND);
                        log.info("ALiceInWonderlandBizManager#sendGiftHandler task_1 taskValue {}",taskValue);
                        String collectedTaskKey=AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(id,AliceInWonderlandConstant.GreenPathTask.GREEN_PATH_TASK_2.getTaskCode(),DateUtil.getNowYyyyMMdd());
                        redisManager.sSetExpire(collectedTaskKey,DateUtil.ONE_MONTH_SECOND,coinGiftGivedModel.getGiftKey());
                    }
                }
                log.info("ALiceInWonderlandBizManager#sendGiftHandler coin {}",coin);
                coin = coin * coinGiftGivedModel.getProductCount();
                //User
                for(Long id : uidList){
                    String userCampKey = AliceInWonderlandConstant.createUserCampRedisKey(id);
                    Integer userCamp =redisManager.getInteger(userCampKey);
                    String totalCampValueKey = AliceInWonderlandConstant.createAliceCampValueRedisKey(userCamp);
                    redisManager.incrLong(totalCampValueKey,coin,DateUtil.ONE_MONTH_SECOND);
                    String userCampValueKey = AliceInWonderlandConstant.createAliceUserCampValueRedisKey(id, userCamp);
                    Long userCampValue=redisManager.incrLong(userCampValueKey,coin,DateUtil.ONE_MONTH_SECOND);
                    log.info("ALiceInWonderlandBizManager#sendGiftHandler userCampValue {}",userCampValue);
                    String userRankKey=AliceInWonderlandConstant.createAliceCampRankRedisKey(userCamp);
                    aliceInWonderlandRankManager.incrRankValue(id,coin,userRankKey);

                }
                //绑定用户，开启深林初遇活动
                String bindKey=AliceInWonderlandConstant.createAliceBindFriendRedisKey(uid);
                Object toBindUid=redisManager.hget(bindKey,String.valueOf(toUid));
                for(Long id : uidList){
                    String openKey = AliceInWonderlandConstant.createAliceDreamLandIsOpenRedisKey(AppUtil.splicUserId(uid,toUid));
                    redisManager.set(openKey,System.currentTimeMillis(),DateUtil.ONE_MONTH_SECOND);
                }
                //没绑定则绑定
                if(ObjectUtil.isNull(toBindUid)){
                    bindFriend(uid,toUid);
                }
                continue;
            }
            //1.深林初遇礼物
            if(!ObjectUtil.equals(coinGiftGivedModel.getType(),"packet")&&
            ObjectUtil.isNull(coinGiftGivedModel.getLotteryGiftKey())){
                String openKey = AliceInWonderlandConstant.createAliceDreamLandIsOpenRedisKey(AppUtil.splicUserId(uid,toUid));
                String openVal = redisManager.getString(openKey);
                if(ObjectUtil.isNull(openVal)){
                    continue;
                }
                String bindKey=AliceInWonderlandConstant.createAliceBindFriendRedisKey(uid);
                Object bindId = redisManager.hget(bindKey,String.valueOf(toUid));
/*
                String feedingRedisKey = AliceInWonderlandConstant.createAliceFeedingRedisKey(String.valueOf(bindId));
*/
                Long feedingValue=null;
                if(ObjectUtil.isNotNull(bindId)){
                    String feedingRedisKey = AliceInWonderlandConstant.createAliceFeedingRedisKey(String.valueOf(bindId));
                    feedingValue =redisManager.incrLong(feedingRedisKey,coinGiftGivedModel.getCoin(),DateUtil.ONE_MONTH_SECOND);
                    log.info("ALiceInWonderlandBizManager#sendGiftHandler feedingValue {}",feedingValue);
                }
                //自动领取奖励
/*
                if(ObjectUtil.isNull(feedingValue)){
                    continue;
                }
                String repeatNumKey = AliceInWonderlandConstant.createRepeatNumRedisKey(uid, AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode());
                Long repeatNum =Optional.ofNullable(redisManager.getLong(repeatNumKey)).orElse(0L);
                if (repeatNum != 0) {
                    feedingValue = feedingValue - (AliceInWonderlandConstant.MAX_FEEDING_VALUE * repeatNum);
                }
                for (AliceInWonderlandConstant.DreamLandReward reward : AliceInWonderlandConstant.DreamLandReward.values()) {
                    if (feedingValue >= reward.getFeedingValue()) {
                        String prizeKey = AliceInWonderlandConstant.createNodeIsReceivedRedisKey(uid, reward.getLevel().toString(), AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode());
                        String prizeIsReceived = redisManager.getString(prizeKey);
                        if (ObjectUtil.isNull(prizeIsReceived)) {
                            aliceInWonderlandTrackManager.allActivityReceiveAward("aris_adventure", reward.getGiftKey(), null, reward.getCount(), reward.getGiftKey().equals("magicWand") ? "starlight_wishes_magicWand" : "starlight_wishes_magicCloak", uid);
                            AwardRequest awardRequest = new AwardRequest();
                            awardRequest.setBaseParam(param);
                            awardRequest.setToUid(toUid);
                            awardRequest.setLevel(reward.getLevel());
                            AwardRequest.Prize prize = new AwardRequest.Prize();
                            prize.setPrizeType(reward.getPrizeType());
                            prize.setPrizeCode(reward.getGiftKey());
                            prize.setPrizeNum(reward.getCount());
                            awardRequest.setPrizes(Arrays.asList(prize));
                            awardRequest.setUid(uid);
                            awardRequest.setScene(AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode());
                            getAward(awardRequest);
                        }
                    }
                }
*/
            }
        }
        return true;
    }



/*
    public List<RankVO> getRank(Integer camp) {
        String rankKey = AliceInWonderlandConstant.createAliceCampRankRedisKey(camp);
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(rankKey,
                0, Double.MAX_VALUE, 0, 10);
        List<RankVO> rankVOS = new ArrayList<>();
        Integer rank = 1;
        for(ZSetOperations.TypedTuple<Object> typedTuple : typedTuples){
            RankVO rankVO = new RankVO();
            String uid=typedTuple.getValue().toString();
            UserVO userVO =feignUserService.getBasic(Long.valueOf(uid), ServicesAppIdEnum.lanling.getAppId()).getData();
            rankVO.setId(userVO.getId());
            rankVO.setUserName(userVO.getName());
            rankVO.setUserAvatar(userVO.getAvatar());
            rankVO.setScore(Long.valueOf(typedTuple.getScore().toString()));
            rankVO.setRank(rank++);
            rankVOS.add(rankVO);
        }
        return rankVOS;
    }
*/

    @Override
    public String getActivityCode() {
        return AliceInWonderlandConstant.ACTIVITY_CODE;
    }

    public List<AliceDrawPoolVO> getDrawPools(Long uid) {
        List<AliceDrawPoolVO> aliceDrawPoolVOS = new ArrayList<>();
        for (AliceInWonderlandConstant.DrawPool drawPool : AliceInWonderlandConstant.DrawPool.values()) {
            List<DrawPoolItemDO> drawPoolItemDO = drawPoolItemJpaDAO.getByPoolCode(drawPool.getPoolCode());
            List<PrizeItem> prizeItems = new ArrayList<>();
            for (DrawPoolItemDO itemDO : drawPoolItemDO) {
                PrizeItem prizeItem = new PrizeItem();
                prizeItem.setPrizeName(itemDO.getItemName());
                prizeItem.setPrizeIcon(itemDO.getItemIcon());
                prizeItem.setPrizeKey(itemDO.getItemKey());
                prizeItem.setPrizeType(itemDO.getItemType());
                prizeItem.setValueGold(Integer.valueOf(itemDO.getItemValueGold().toString()));
                prizeItems.add(prizeItem);
            }
            String key = AliceInWonderlandConstant.createAliceDrawItemRedisKey(drawPool.getPoolCode(), uid);
            Integer itemNum = redisManager.getInteger(key);
            AliceDrawPoolVO aliceDrawPoolVO = new AliceDrawPoolVO();
            aliceDrawPoolVO.setLotteryItem(itemNum);
            aliceDrawPoolVO.setPrizeItemList(prizeItems);
            aliceDrawPoolVO.setPoolCode(drawPool.getPoolCode());
            aliceDrawPoolVOS.add(aliceDrawPoolVO);
        }
        return aliceDrawPoolVOS;
    }

    public DreamLandVO getDreamLand(Long uid,Long toUid) {
        DreamLandVO dreamLandVO = new DreamLandVO();

        initUserInfo(uid,toUid,dreamLandVO);

        initNodes(uid,dreamLandVO);
        if (toUid == null) {
            if(ObjectUtil.isNotNull(dreamLandVO.getFriendInfo())){
                toUid = dreamLandVO.getFriendInfo().getUid();
            }
        }
        String key = AliceInWonderlandConstant.createAliceDreamLandIsOpenRedisKey(AppUtil.splicUserId(uid,toUid));
        Object isOpen = redisManager.getString(key);
        dreamLandVO.setIsOpen(ObjectUtil.isNotNull(isOpen));
        return dreamLandVO;
    }

    private void initNodes(Long uid, DreamLandVO dreamLandVO) {
        List<DreamLandVO.Nodes> nodes = new ArrayList<>();
        Long toUid=null;
        if(ObjectUtil.isNotNull(dreamLandVO.getFriendInfo())){
            toUid=dreamLandVO.getFriendInfo().getUid();
        }
        String bindKey = AliceInWonderlandConstant.createAliceBindFriendRedisKey(uid);
        Object bindValue = redisManager.hget(bindKey,String.valueOf(toUid));
        String feedValueKey=AliceInWonderlandConstant.createAliceFeedingRedisKey(String.valueOf(bindValue));
/*
        Long feedingValue=Optional.ofNullable(redisManager.getLong(AliceInWonderlandConstant.createAliceFeedingRedisKey(feedValueKey))).orElse(0L);
*/
        Long feedingValue=Optional.ofNullable(redisManager.getLong(feedValueKey)).orElse(0L);
        String repeatNumKey = AliceInWonderlandConstant.createRepeatNumRedisKey(String.valueOf(AppUtil.splicUserId(uid,toUid)), AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode());
        Long repeatNum =Optional.ofNullable(redisManager.getLong(repeatNumKey)).orElse(0L);
/*
        if (repeatNum != 0) {
            feedingValue = feedingValue - (AliceInWonderlandConstant.MAX_FEEDING_VALUE * repeatNum);
        }
*/
        if (feedingValue > AliceInWonderlandConstant.MAX_FEEDING_VALUE) {
            feedingValue = AliceInWonderlandConstant.MAX_FEEDING_VALUE;
        }
        log.info("AliceInWonderlandBizManager#initNodes feedingValue {} repeatNum {}",feedingValue,repeatNum);
        dreamLandVO.setFeedingValue(feedingValue);
        Integer curLevel = 0;
        for(AliceInWonderlandConstant.DreamLandReward nodesEnum : AliceInWonderlandConstant.DreamLandReward.values()){
            DreamLandVO.Nodes nodeVO = new DreamLandVO.Nodes();
            nodeVO.setLevel(nodesEnum.getLevel());
/*
            String key = AliceInWonderlandConstant.createAliceDreamLandIsReceivedRedisKey(uid,nodesEnum.getLevel());
*/
            String repeatStr = ObjectUtil.isNull(repeatNum) ? "0" : repeatNum.toString();
            String key= AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(AppUtil.splicUserId(uid,toUid)),nodesEnum.getLevel().toString()+":"+repeatStr,AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode());
            String isReceived = redisManager.getString(key);
            log.info("AliceInWonderlandBizManager#initNodes isReceivedKey {} isReceived {}",key,isReceived);
            nodeVO.setIsReceived(ObjectUtil.isNotNull(isReceived));
            nodeVO.setNeedFeedingValue(nodesEnum.getFeedingValue());
            //节点解锁奖励
            AliceGiftVO reward = new AliceGiftVO();
            reward.setName(nodesEnum.getGiftName());
            reward.setCount(nodesEnum.getCount());
            reward.setKey(nodesEnum.getGiftKey());
            reward.setPrizeType(nodesEnum.getPrizeType());
            nodeVO.setRewards(reward);
            //是否解锁
            Boolean isUnlock = feedingValue >= nodesEnum.getFeedingValue();
            if(isUnlock){
                aliceInWonderlandTrackManager.allActivityTaskFinish(uid,"forest_first_meet",nodesEnum.getGiftKey().equals("magicCloak")?"starlight_wishes_magicCloak":"starlight_wishes_magicWand",nodesEnum.getGiftKey(),nodesEnum.getCount());
                curLevel=nodesEnum.getLevel();
            }
            nodeVO.setIsUnlock(isUnlock);
            nodeVO.setRepeat(repeatNum);
            nodes.add(nodeVO);
        }
        dreamLandVO.setNodes(nodes);
        dreamLandVO.setCurLevel(curLevel);
    }

    private void initUserInfo(Long uid,Long toUid ,DreamLandVO dreamLandVO) {
        UserVO userVO = feignUserService.getBasic(Long.valueOf(uid), ServicesAppIdEnum.lanling.getAppId()).getData();
        DreamLandVO.UserInfoVO userInfoVO = new DreamLandVO.UserInfoVO();
        userInfoVO.setUserName(userVO.getName());
        userInfoVO.setAvatar(userVO.getAvatar());
        userInfoVO.setUid(userVO.getId());
        Integer userCamp = redisManager.getInteger(AliceInWonderlandConstant.createUserCampRedisKey(uid));
        userInfoVO.setCamp(userCamp);
        dreamLandVO.setUserInfo(userInfoVO);
        String hashKey=AliceInWonderlandConstant.createAliceBindFriendRedisKey(uid);
        Set<Object> keys =redisManager.hKeys(hashKey);
        if(ObjectUtil.isNull(keys)){
            return;
        }
        if(ObjectUtil.isNotNull(toUid)){
            Object bindId=redisManager.hget(hashKey,toUid.toString());
            if(ObjectUtil.isNotNull(bindId)){
                UserVO toUserVO = feignUserService.getBasic(Long.valueOf(toUid), ServicesAppIdEnum.lanling.getAppId()).getData();
                DreamLandVO.UserInfoVO friendInfoVO = new DreamLandVO.UserInfoVO();
                friendInfoVO.setUserName(toUserVO.getName());
                friendInfoVO.setAvatar(toUserVO.getAvatar());
                friendInfoVO.setUid(Long.valueOf(toUid));
                dreamLandVO.setFriendInfo(friendInfoVO);
                return;
            }
        }
        //toUid为空，显示找亲密度最高的密友
        Integer maxIntimacy=0;
        for(Object key : keys){
            UserIntimacyDTO intimacy =feignSnsService.getUserIntimacy(userVO.getAppId(),uid,Long.valueOf(key.toString())).getData();
            if (maxIntimacy < intimacy.getIntimacy().intValue()) {
                toUid = Long.valueOf(key.toString());
            }
            maxIntimacy=Math.max(maxIntimacy,intimacy.getIntimacy().intValue());
        }
        if(ObjectUtil.isNull(toUid)){
            return;
        }
        UserVO toUserVO = feignUserService.getBasic(Long.valueOf(toUid), ServicesAppIdEnum.lanling.getAppId()).getData();
        DreamLandVO.UserInfoVO friendInfoVO = new DreamLandVO.UserInfoVO();
        friendInfoVO.setUserName(toUserVO.getName());
        friendInfoVO.setAvatar(toUserVO.getAvatar());
        friendInfoVO.setUid(Long.valueOf(toUid));
        Integer friendCamp=redisManager.getInteger(AliceInWonderlandConstant.createUserCampRedisKey(toUid));
        friendInfoVO.setCamp(friendCamp);
        dreamLandVO.setFriendInfo(friendInfoVO);
    }

    private List<AliceGiftVO> getPropRewards(Long toUid) {
        List<AliceGiftVO> propRewards = new ArrayList<>();
        for(AliceInWonderlandConstant.DreamLandPropReward propReward : AliceInWonderlandConstant.DreamLandPropReward.values()){
            AliceGiftVO alicePropReward = new AliceGiftVO();
            alicePropReward.setName(propReward.getGiftName());
            alicePropReward.setKey(propReward.getGiftKey());
            alicePropReward.setPrizeType(propReward.getGiftType());
            alicePropReward.setCount(propReward.getCount());
            propRewards.add(alicePropReward);
        }
        return propRewards;
    }

    private List<ForestWonderlandVO.GiftWallVO> getGiftWall(Long uid) {
        List<ForestWonderlandVO.GiftWallVO> giftWallVOS = new ArrayList<>();
        String repeatNumKey = AliceInWonderlandConstant.createRepeatNumRedisKey(String.valueOf(uid), AliceInWonderlandConstant.Scenes.FOREST_WONDERLAND.getSceneCode());
        Long repeatNum = Optional.ofNullable(redisManager.getLong(repeatNumKey)).orElse(0L);
        for (AliceInWonderlandConstant.GiftWall giftWall : AliceInWonderlandConstant.GiftWall.values()) {
            ForestWonderlandVO.GiftWallVO giftWallVO = new ForestWonderlandVO.GiftWallVO();
            String key = AliceInWonderlandConstant.createAliceGiftWallCollectedRedisKey(uid, giftWall.getGiftKey());
            Long collected = Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
            if(repeatNum!=0){
                collected = collected - (giftWall.getRequireNum()*repeatNum);
            }
            giftWallVO.setGiftName(giftWall.getGiftName());
            giftWallVO.setValueGold(giftWall.getValueGold());
            giftWallVO.setIndex(giftWall.getIndex());
            giftWallVO.setRequireCount(giftWall.getRequireNum());
            giftWallVO.setCollectCount(collected);
            giftWallVO.setIsComplete(collected >= giftWall.getRequireNum());
            giftWallVOS.add(giftWallVO);
        }
        return giftWallVOS;
    }

    public GreenPathVO getGreenPath(Long uid) {
        GreenPathVO greenPathVO = new GreenPathVO();
        List<GreenPathVO.nodesVO> nodesVOS = new ArrayList<>();
        String key = AliceInWonderlandConstant.createGreenPathCollectedRedisKey(uid);
        Long collected = Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
        Long repeatNum = Optional.ofNullable(redisManager.getLong(AliceInWonderlandConstant.createRepeatNumRedisKey(String.valueOf(uid), AliceInWonderlandConstant.Scenes.GREEN_PATH.getSceneCode()))).orElse(0L);
        if (repeatNum != 0) {
            collected = collected - (AliceInWonderlandConstant.MAX_BERRIES_VALUE*repeatNum);
        }
        greenPathVO.setBerries(collected);
        Integer curLevel = 0;
        for(AliceInWonderlandConstant.GreenPath greenPath : AliceInWonderlandConstant.GreenPath.values()){
            GreenPathVO.nodesVO nodeVO = new GreenPathVO.nodesVO();
            nodeVO.setLevel(greenPath.getIndex());
            nodeVO.setRewards(greenPath.getGiftlist());
            Boolean unlockStatus = collected >= greenPath.getRequireNum();
            if (unlockStatus) {
                curLevel = greenPath.getIndex();
                for(AliceGiftVO nodesEnum:greenPath.getGiftlist()){
                    aliceInWonderlandTrackManager.allActivityTaskFinish(uid, "wood_room_danger", nodesEnum.getKey().equals("magicCloak") ? "starlight_wishes_magicCloak" : "starlight_wishes_magicWand", nodesEnum.getKey(), nodesEnum.getCount());
                }
                //添加用户值
                String userCampValueIsReceivedRedisKey=AliceInWonderlandConstant.createAliceUserCampValueIsRecivedRedisKey(uid,greenPath.getIndex().toString(),AliceInWonderlandConstant.Scenes.GREEN_PATH.getSceneCode());
                log.info("ALiceInWonderlandBizManager#getGreenPath campValueIsReceivedRedisKey {}",userCampValueIsReceivedRedisKey);
                if (ObjectUtil.isNull(redisManager.getString(userCampValueIsReceivedRedisKey)) && greenPath.getIndex() > 0) {
                    String userCamp=AliceInWonderlandConstant.createUserCampRedisKey(uid);
                    Integer camp=redisManager.getInteger(userCamp);
                    String userCampValueKey=AliceInWonderlandConstant.createAliceUserCampValueRedisKey(uid,camp);
                    redisManager.incrLong(userCampValueKey,1000,DateUtil.ONE_MONTH_SECOND);
                    String campTotalValueKey=AliceInWonderlandConstant.createAliceCampValueRedisKey(camp);
                    redisManager.incrLong(campTotalValueKey,1000,DateUtil.ONE_MONTH_SECOND);
                    String userRankKey=AliceInWonderlandConstant.createAliceCampRankRedisKey(camp);
                    aliceInWonderlandRankManager.incrRankValue(uid,1000L,userRankKey);
                    redisManager.set(userCampValueIsReceivedRedisKey,System.currentTimeMillis(),DateUtil.ONE_MONTH_SECOND);
                }

            }
            nodeVO.setUnlockStatus(unlockStatus);
            nodeVO.setCollected(collected);
            nodeVO.setRequiredProgress(greenPath.getRequireNum());
            key=AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(uid),greenPath.getIndex().toString(),AliceInWonderlandConstant.Scenes.GREEN_PATH.getSceneCode());
            String isRewardClaimed = redisManager.getString(key);
            nodeVO.setIsRewardClaimed(ObjectUtil.isNotNull(isRewardClaimed));
            nodesVOS.add(nodeVO);
        }
        greenPathVO.setNodes(nodesVOS);
        greenPathVO.setCurLevel(curLevel);
        initTask(uid,greenPathVO);
        return greenPathVO;
    }

    private void initTask(Long uid,GreenPathVO greenPathVO) {
        List<GreenPathVO.TaskVO> taskVOS = new ArrayList<>();
        for (AliceInWonderlandConstant.GreenPathTask greenPathTask : AliceInWonderlandConstant.GreenPathTask.values()) {
            GreenPathVO.TaskVO taskVO = new GreenPathVO.TaskVO();
            taskVO.setTaskCode(greenPathTask.getTaskCode());
            taskVO.setProgress(greenPathTask.getRequiredProgress());
            taskVO.setRequiredProgress(greenPathTask.getRequiredProgress());
            String key = AliceInWonderlandConstant.createAliceGreenPathTaskProgressRedisKey(uid, greenPathTask.getTaskCode(), DateUtil.getNowYyyyMMdd());
            Long progress;
            if (AliceInWonderlandConstant.REPEAT_TASK_KEY.contains(greenPathTask.getTaskCode())) {
                progress = Optional.ofNullable(redisManager.sGetSetSize(key)).orElse(0L);
            } else {
                progress = Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
            }
            taskVO.setIsRepeat(greenPathTask.getIsRepeat());
            progress = progress >= greenPathTask.getRequiredProgress() ? greenPathTask.getRequiredProgress() : progress;
            taskVO.setProgress(progress.intValue());
            String isTaskReceived = AliceInWonderlandConstant.createGreenPathTaskPrizeIsReceivedRedisKey(uid, greenPathTask.getTaskCode(), DateUtil.getNowYyyyMMdd());
            String isReceived = redisManager.getString(isTaskReceived);
            taskVO.setIsFinished(progress >= greenPathTask.getRequiredProgress());
            taskVO.setIsReceived(ObjectUtil.isNotNull(isReceived));
            taskVOS.add(taskVO);
        }
        greenPathVO.setTask(taskVOS);
    }

    public ForestWonderlandVO getForestWonderland(Long uid) {
        ForestWonderlandVO forestWonderlandVO = new ForestWonderlandVO();
        List<ForestWonderlandVO.GiftWallVO> giftWallVOS = getGiftWall(uid);
        forestWonderlandVO.setGiftWall(giftWallVOS);
        List<AliceGiftVO> propRewards = getPropRewards(uid);
        forestWonderlandVO.setPropRewards(propRewards);
        Boolean canReceiveRewards = true;
        for(ForestWonderlandVO.GiftWallVO giftWallVO : giftWallVOS){
            if(!giftWallVO.getIsComplete()){
                canReceiveRewards = false;
            }
        }
        //任务完成埋点
        if(canReceiveRewards){
            for (AliceGiftVO propReward : propRewards) {
                aliceInWonderlandTrackManager.allActivityTaskFinish(uid, "explore_forest", "magicWand".equals(propReward.getKey())?"starlight_wishes_magicWand":null,propReward.getKey(),propReward.getCount());
            }
        }
        forestWonderlandVO.setCanReceiveRewards(canReceiveRewards);
        String key = AliceInWonderlandConstant.createAliceForestWonderlandPropRewardsRedisKey(uid);
        String isReceived =redisManager.getString(key);
        forestWonderlandVO.setIsReceived(ObjectUtil.isNotNull(isReceived));
        return forestWonderlandVO;
    }

    public List<DreamLandVO.UserInfoVO> getFriendList(Long uid) {
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(MDCUtil.getCurUserIdByMdc()).successData();
        List<DreamLandVO.UserInfoVO> result = new ArrayList<>();
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return result;
        }

        allUserDepth.forEach((k, v) -> {
            UserVO userVO = feignUserService.getBasic(k, MDCUtil.getCurAppIdByMdc()).successData();
            if (userVO != null) {
                DreamLandVO.UserInfoVO userInfoVO = new DreamLandVO.UserInfoVO();
                userInfoVO.setUid(userVO.getId());
                userInfoVO.setUserName(userVO.getName());
                userInfoVO.setAvatar(userVO.getAvatar());
                String key=AliceInWonderlandConstant.createUserCampRedisKey(userVO.getId());
                Integer camp =Optional.ofNullable( redisManager.getInteger(key)).orElse(null);
                userInfoVO.setCamp(camp);
                result.add(userInfoVO);
            }
        });

        return result;
    }

    public Boolean bindFriend(Long uid, Long toUid) {
        //判断是否是密友
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(MDCUtil.getCurUserIdByMdc()).successData();
        if (ObjectUtil.isNotNull(allUserDepth)) {
            if (!allUserDepth.containsKey(toUid)) {
                return false;
            }
        } else {
            return false;
        }
        //1.判断是否是相同阵营
        if(ObjectUtil.isNull(uid)||ObjectUtil.isNull(toUid)){
            return false;
        }
        Integer userCamp=redisManager.getInteger(AliceInWonderlandConstant.createUserCampRedisKey(uid));
        Integer toUserCamp=redisManager.getInteger(AliceInWonderlandConstant.createUserCampRedisKey(toUid));
        if(ObjectUtil.isNull(userCamp)||ObjectUtil.isNull(toUserCamp)){
            return false;
        }
        String key = AliceInWonderlandConstant.createActivityIsBindFriendRedisKey(AppUtil.splicUserId(uid,toUid));
        String isBindFriend = redisManager.getString(key);
        log.info("AliceInWonderlandBizManager#bindFriend key {} isBindFriend {}",key,isBindFriend);
        if(ObjectUtil.isNotNull(isBindFriend)){
            return false;
        }
        if(ObjectUtil.equal(userCamp,toUserCamp)){
            //增加奇幻值/幻想值
            String userCampValueKey=AliceInWonderlandConstant.createAliceUserCampValueRedisKey(uid,userCamp);
            String userRankKey=AliceInWonderlandConstant.createAliceCampRankRedisKey(userCamp);
            aliceInWonderlandRankManager.incrRankValue(uid,10000L,userRankKey);
            Long userCampValue=redisManager.incrLong(userCampValueKey,10000,DateUtil.ONE_MONTH_SECOND);
            log.info("AliceInWonderlandBizManager#bindFriend userCampValue {}",userCampValue);
            String toUserCampValueKey=AliceInWonderlandConstant.createAliceUserCampValueRedisKey(toUid,toUserCamp);
            String toUserRankKey=AliceInWonderlandConstant.createAliceCampRankRedisKey(toUserCamp);
            aliceInWonderlandRankManager.incrRankValue(toUid,10000L,toUserRankKey);
            Long toUserCampValue= redisManager.incrLong(toUserCampValueKey,10000,DateUtil.ONE_MONTH_SECOND);
            log.info("AliceInWonderlandBizManager#bindFriend toUserCampValue {}",toUserCampValue);
            String totalValueKey=AliceInWonderlandConstant.createAliceCampValueRedisKey(userCamp);
            redisManager.incrLong(totalValueKey,20000,DateUtil.ONE_MONTH_SECOND);
        }

        String bindingId=UUID.randomUUID().toString();
        String bindKey = AliceInWonderlandConstant.createAliceBindFriendRedisKey(uid);
        Boolean uidIsSuccess =redisManager.hset(bindKey,String.valueOf(toUid),bindingId);
        String toBindKey=AliceInWonderlandConstant.createAliceBindFriendRedisKey(toUid);
        Boolean toUidIsSuccess =redisManager.hset(toBindKey,String.valueOf(uid),bindingId);
        log.info("AliceInWonderlandBizManager#bindFriend uid {} toUid {} bindingId {}",uidIsSuccess,toUidIsSuccess,bindingId);
        if (!uidIsSuccess || !toUidIsSuccess) {
            redisManager.hdel(bindKey,String.valueOf(toUid));
            redisManager.hdel(bindKey,String.valueOf(uid));
        }
        redisManager.set(key,bindingId,DateUtil.ONE_MONTH_SECOND);
        return uidIsSuccess && toUidIsSuccess;
    }


    @NoRepeatSubmit(time = 2)
    public Boolean getAward(AwardRequest awardRequest) {
        Long uid;
        if(ObjectUtil.isNull(awardRequest.getUid())){
            SecurityUser user = SecurityUtils.getCurrentUser();
            uid=user.getUserId();
        }else{
            uid=awardRequest.getUid();
        }
        List<AwardRequest.Prize> prizes=awardRequest.getPrizes();
        BaseParam param=awardRequest.getBaseParam();
        Long toUid=awardRequest.getToUid();
        Boolean isForestWonderland=AliceInWonderlandConstant.Scenes.FOREST_WONDERLAND.getSceneCode().equals(awardRequest.getScene());
        if(isForestWonderland){
            String isForestWonderlandReceivedKey=AliceInWonderlandConstant.createAliceForestWonderlandPropRewardsRedisKey(uid);
            String isReceived = redisManager.getString(isForestWonderlandReceivedKey);
            if(ObjectUtil.isNotNull(isReceived)){
                return false;
            }
        }
        String isReceivedKey=null;
        for(AwardRequest.Prize reward:prizes){
            if(ObjectUtil.equals(reward.getPrizeType(),"dress_up")){
                List<ScenePrizeDO> scenePrizeDOList=scenePrizeService.getListBySceneCode(AliceInWonderlandConstant.ACTIVITY_CODE,"forest_wonderland");
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                        );
                String msg="恭喜访客获得“仙境收集”头像框，奖励下发装扮，快去查收吧~";
                notifyComponent.npcNotify(param.getUnionId(),param.getUid(),msg);
            }
            if(ObjectUtil.equals(reward.getPrizeType(),"draw_item")){
                String isReceived;
                if(!isForestWonderland){
                    isReceivedKey = AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(uid), awardRequest.getLevel().toString(), awardRequest.getScene());
                } else{
                    isReceivedKey = AliceInWonderlandConstant.createAliceForestWonderlandPropRewardsRedisKey(uid);
                }
                if (AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode().equals(awardRequest.getScene())) {
                    isReceivedKey = AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(AppUtil.splicUserId(uid,toUid)), awardRequest.getLevel()+":"+awardRequest.getRepeat(), awardRequest.getScene());
                    log.info("AliceInWonderlandBizManager#getAward dreamLand isReceivedKey {}",isReceivedKey);
                    if(ObjectUtil.isNull(toUid)){
                        isReceivedKey=null;
                    }
                }
                isReceived=redisManager.getString(isReceivedKey);
                log.info("AliceInWonderlandBizManager#getAward dreamLand isReceived {}",isReceived);
                if(ObjectUtil.isNotNull(isReceived)){
                    continue;
                }
                AliceInWonderlandConstant.DrawPool drawPool= AliceInWonderlandConstant.DrawPool.getByItem(reward.getPrizeCode());
                String key=AliceInWonderlandConstant.createAliceDrawItemRedisKey(drawPool.getPoolCode(),uid);
                redisManager.incrLong(key,reward.getPrizeNum(),DateUtil.ONE_MONTH_SECOND);
                //奖励领取埋点
                String taskType="";
                String msg="";
                if (AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode().equals(awardRequest.getScene())
                        && AliceInWonderlandConstant.DrawPool.STARLIGHT_WISHES_MAGICCLOAK.getItem().equals(reward.getPrizeCode())) {
                    msg="亲爱的访客，恭喜您在“深林初遇”中获得能量宝石*%s，快去抽奖吧~";
                    taskType="forest_first_meet";
                }
                if(AliceInWonderlandConstant.Scenes.DREAM_LAND.getSceneCode().equals(awardRequest.getScene())
                && AliceInWonderlandConstant.DrawPool.STARLIGHT_WISHES_MAGICWAND.getItem().equals(reward.getPrizeCode())){
                    msg="亲爱的访客，恭喜您在“深林初遇”中获得智慧权杖*%s，快去抽奖吧~";
                    taskType="forest_first_meet";
                }
                if (AliceInWonderlandConstant.Scenes.GREEN_PATH.getSceneCode().equals(awardRequest.getScene())
                        && AliceInWonderlandConstant.DrawPool.STARLIGHT_WISHES_MAGICCLOAK.getItem().equals(reward.getPrizeCode())) {
                    msg="亲爱的访客，恭喜您在“木屋危机”中获得能量宝石*%s，快去抽奖吧~";
                    taskType="wood_room_danger";
                }
                if(AliceInWonderlandConstant.Scenes.GREEN_PATH.getSceneCode().equals(awardRequest.getScene())
                        && AliceInWonderlandConstant.DrawPool.STARLIGHT_WISHES_MAGICWAND.getItem().equals(reward.getPrizeCode())){
                    msg="亲爱的访客，恭喜您在“木屋危机”中获得智慧权杖*%s，快去抽奖吧~";
                    taskType="wood_room_danger";
                }
                if (AliceInWonderlandConstant.Scenes.FOREST_WONDERLAND.getSceneCode().equals(awardRequest.getScene())
                        && AliceInWonderlandConstant.DrawPool.STARLIGHT_WISHES_MAGICCLOAK.getItem().equals(reward.getPrizeCode())) {
                    msg="亲爱的访客，恭喜您在“丛林探秘”中获得能量宝石*%s，快去抽奖吧~";
                    taskType="explore_forest";
                }
                if(AliceInWonderlandConstant.Scenes.FOREST_WONDERLAND.getSceneCode().equals(awardRequest.getScene())
                        && AliceInWonderlandConstant.DrawPool.STARLIGHT_WISHES_MAGICWAND.getItem().equals(reward.getPrizeCode())){
                    msg="亲爱的访客，恭喜您在“丛林探秘”中获得智慧权杖*%s，快去抽奖吧~";
                    taskType="explore_forest";
                }
                aliceInWonderlandTrackManager.allActivityReceiveAward(taskType, reward.getPrizeCode(), null, reward.getPrizeNum(), null, uid);
                notifyComponent.npcNotify(ServicesAppIdEnum.lanling.getUnionId(),uid,String.format(msg,reward.getPrizeNum()));
                String isToUserGetRewardKey=AliceInWonderlandConstant.createAliceIsToUserGetRewardRedisKey(AppUtil.splicUserId(uid,toUid));
                if (ObjectUtil.isNotNull(toUid) && ObjectUtil.isNull(redisManager.getString(isToUserGetRewardKey))) {
                    //好友一起领取
                    AwardRequest toUserAwardRequest=new AwardRequest();
                    BeanUtil.copyProperties(awardRequest,toUserAwardRequest);
                    toUserAwardRequest.setUid(toUid);
                    toUserAwardRequest.setToUid(uid);
                    redisManager.set(isToUserGetRewardKey, System.currentTimeMillis(), DateUtil.ONE_DAY_SECONDS);
                    getAward(toUserAwardRequest);
                    redisManager.delete(isToUserGetRewardKey);
                }
            }
        }
        if(ObjectUtil.isNotNull(isReceivedKey)){
            redisManager.set(isReceivedKey, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
        }
        AliceInWonderlandConstant.Scenes scenes = AliceInWonderlandConstant.Scenes.getScenesByCode(awardRequest.getScene());
        //木屋危机清除节点进度
        if(AliceInWonderlandConstant.Scenes.GREEN_PATH.equals(scenes)){
            Integer isReceivedNum=0;
            for(AliceInWonderlandConstant.GreenPath node:AliceInWonderlandConstant.GreenPath.values()){
                if(node.getIndex()==0){
                    continue;
                }
                String key=AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(uid),node.getIndex().toString(),scenes.getSceneCode());
                String isReceived=redisManager.getString(key);
                if(ObjectUtil.isNotNull(isReceived)){
                    isReceivedNum++;
                }
            }
            //木屋危机领取完之后清除进度
            if (isReceivedNum == AliceInWonderlandConstant.GreenPath.values().length - 1) {
                for(AliceInWonderlandConstant.GreenPath node:AliceInWonderlandConstant.GreenPath.values()){
                    String key=AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(uid),node.getIndex().toString(),scenes.getSceneCode());
                    redisManager.delete(key);
                    String userCampValueIsRecivedRedisKey=AliceInWonderlandConstant.createAliceUserCampValueIsRecivedRedisKey(uid,node.getIndex().toString(),AliceInWonderlandConstant.Scenes.GREEN_PATH.getSceneCode());
                    redisManager.delete(userCampValueIsRecivedRedisKey);
                }
                String repeatNumKey=AliceInWonderlandConstant.createRepeatNumRedisKey(String.valueOf(uid),scenes.getSceneCode());
                redisManager.incrLong(repeatNumKey,1,DateUtil.ONE_MONTH_SECOND);
            }
            return Boolean.TRUE;
        }
        //深林初遇清除节点领取进度
        if(AliceInWonderlandConstant.Scenes.DREAM_LAND.equals(scenes)){
            String repeatNumKey=AliceInWonderlandConstant.createRepeatNumRedisKey(String.valueOf(AppUtil.splicUserId(uid,toUid)),scenes.getSceneCode());
            Long repeatNum=Optional.ofNullable(redisManager.getLong(repeatNumKey)).orElse(0L);
            if(!ObjectUtil.equals(repeatNum,awardRequest.getRepeat())){
                return Boolean.TRUE;
            }
            String isToUserGetRewardKey=AliceInWonderlandConstant.createAliceIsToUserGetRewardRedisKey(AppUtil.splicUserId(uid,toUid));
            String isToUser=redisManager.getString(isToUserGetRewardKey);
            if(ObjectUtil.isNotNull(isToUser)){
                return Boolean.TRUE;
            }
            Integer isReceivedNum=0;
            for(AliceInWonderlandConstant.DreamLandReward node:AliceInWonderlandConstant.DreamLandReward.values()){
                String key=AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(AppUtil.splicUserId(uid,toUid)),node.getLevel().toString()+":"+awardRequest.getRepeat(),scenes.getSceneCode());
                String isReceived=redisManager.getString(key);
                if(ObjectUtil.isNotNull(isReceived)){
                    isReceivedNum++;
                }
            }
            if(isReceivedNum == AliceInWonderlandConstant.DreamLandReward.values().length){
/*
                for(AliceInWonderlandConstant.DreamLandReward node:AliceInWonderlandConstant.DreamLandReward.values()){
                    String key=AliceInWonderlandConstant.createNodeIsReceivedRedisKey(String.valueOf(AppUtil.splicUserId(uid,toUid)),node.getLevel().toString(),scenes.getSceneCode());
                    redisManager.delete(key);
                }
*/
                String bindKey = AliceInWonderlandConstant.createAliceBindFriendRedisKey(uid);
                Object bindValue = redisManager.hget(bindKey,String.valueOf(toUid));
                String feedValueKey=AliceInWonderlandConstant.createAliceFeedingRedisKey(String.valueOf(bindValue));
                redisManager.set(feedValueKey,0,DateUtil.ONE_MONTH_SECOND);
                redisManager.incrLong(repeatNumKey,1,DateUtil.ONE_MONTH_SECOND);
            }
            return Boolean.TRUE;
        }
        if(AliceInWonderlandConstant.Scenes.FOREST_WONDERLAND.equals(scenes)){
            String repeatNumKey = AliceInWonderlandConstant.createRepeatNumRedisKey(String.valueOf(uid), AliceInWonderlandConstant.Scenes.FOREST_WONDERLAND.getSceneCode());
            Long repeatNum = Optional.ofNullable(redisManager.getLong(repeatNumKey)).orElse(0L);
            Boolean canReceiveRewards = true;
            for(AliceInWonderlandConstant.GiftWall node :AliceInWonderlandConstant.GiftWall.values() ){
                String key = AliceInWonderlandConstant.createAliceGiftWallCollectedRedisKey(uid, node.getGiftKey());
                Long collected = Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
                if(repeatNum!=0){
                    collected = collected - (node.getRequireNum()*repeatNum);
                }
                if (collected < node.getRequireNum()) {
                    canReceiveRewards = false;
                }
            }
            if(canReceiveRewards){
                String key=AliceInWonderlandConstant.createAliceForestWonderlandPropRewardsRedisKey(uid);
                redisManager.delete(key);
                redisManager.incrLong(repeatNumKey,1,DateUtil.ONE_MONTH_SECOND);
            }
        }
        return Boolean.TRUE;
    }
}
