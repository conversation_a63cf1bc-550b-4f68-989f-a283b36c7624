package cn.yizhoucp.ump.biz.project.biz.remoteService.coin;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.dto.product.CoinGiftInternationParam;
import cn.yizhoucp.ms.core.dto.product.CoinGiftProductDTO;
import cn.yizhoucp.ms.core.vo.coinservices.*;
import cn.yizhoucp.ms.core.vo.userservices.AccountBalanceChangeResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * coin 服务接口
 *
 * <AUTHOR>
 */
@FeignClient("coin-services")
public interface FeignCoinService {

    /**
     * 获取礼物订单总金币/积分
     *
     * @param request 查询参数
     * @return List<CoinGiftOrderModel>
     */
    @PostMapping("/api/inner/get-gift-order-amount")
    Result<Integer> getGiftOrderAmount(@RequestBody GiftOrderQueryVO request);

    /**
     * 获取赠送礼物付费/免费金币（排除福袋）
     *
     * @param uid       送礼用户id
     * @param toUid     收礼用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return Map<String, Long>
     */
    @GetMapping("/api/inner/coin/get-send-gift-value")
    Result<Map<String, Long>> getSendGiftValue(@RequestParam("uid") Long uid, @RequestParam("toUid") Long toUid, @RequestParam("startTime") Long startTime, @RequestParam("endTime") Long endTime);

    /**
     * 根据礼物 key 更新礼物 banner
     *
     * @param appId   应用id
     * @param giftKey 礼物key
     * @param config  banner 配置
     * @return Boolean
     */
    @RequestMapping("/api/inner/update-gift-banner-by-key")
    Result<Boolean> updateGiftBannerByKey(@RequestParam("appId") Long appId, @RequestParam("giftKey") String giftKey, @RequestParam("config") String config);

    /**
     * 更新用户账户信息
     * 接口记录流水，不记流失的直接调用 user 服务 /api/inner/user-account/account-balance-change
     *
     * @param param 请求参数
     * @return AccountBalanceChangeResultVO
     */
    @PostMapping("/api/inner/coin/account/account-balance-change-with-log")
    Result<AccountBalanceChangeResultVO> accountBalanceChangeWithLog(@RequestBody AccountBalanceChangeVO param);

    /**
     * ======================================================= coin 拆分过渡接口 start ===========================================================
     */
    @PostMapping("/api/inner/coin/save-coin-log-new")
    Result<Boolean> saveCoinLog(@RequestBody CoinLogVO coinLog);

    @PostMapping("/api/inner/coin-gift/find-internation")
    Result<List<CoinGiftProductDTO>> findInternationByIdList(@RequestBody CoinGiftInternationParam param);

    /**
     * ======================================================= coin 拆分过渡接口 end ===========================================================
     */

//    @GetMapping("/api/inner/coin/find-internation-name-by-app-id-and-item-key-and-language")
//    Result<String> findInternationNameByAppIdAndItemKeyAndLanguage(@RequestParam("appId") Long appId, @RequestParam("itemKey") String itemKey, @RequestParam("language") String language);

//    @GetMapping("/api/inner/coin/find-internation-name-by-app-id-and-item-key-in-and-language")
//    Result<Map<String, String>> findInternationNameByAppIdAndItemKeyInAndLanguage(@RequestParam("appId") Long appId, @RequestParam("itemKeys") String itemKeys, @RequestParam("language") String language);

    /**
     * 计算两个人的亲密度信息  newIncimacy当前亲密度
     *
     * @param userId
     * @param otherUserId
     * @param appId
     * @return
     */
    @RequestMapping("/api/inner/count-incimacy")
    Result<CoinAndIncimacy> getCoinSumOfTwoPersons(@RequestParam("userId") Long userId,
                                                   @RequestParam("otherUserId") Long otherUserId,
                                                   @RequestParam("appId") Long appId);

//    /**
//     * 根据礼物id获取礼物信息
//     *
//     * @param productId 礼物id
//     * @return
//     */
//    @RequestMapping("/api/inner/get-gift-info")
//    Result<GiftInfoVO> getGiftInfo(@RequestParam("productId") Long productId);

    /**
     * 根据礼物 key 获取礼物信息
     *
     * @param appId   应用id
     * @param giftKey 礼物key
     * @return GiftInfoVO
     */
    @RequestMapping("/api/inner/get-gift-info-by-key")
    Result<GiftInfoVO> getGiftInfoByKey(@RequestParam("appId") Long appId, @RequestParam("giftKey") String giftKey);


}
