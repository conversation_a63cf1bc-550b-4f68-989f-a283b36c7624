package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.RedPacketRecordDO;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface RedPacketRecordJpaDAO extends CrudRepository<RedPacketRecordDO, Long> {

    /**
     * 获取红包的领取记录
     *
     * @return
     */
    List<RedPacketRecordDO> findByRedPacketIdOrderByCreateTimeDesc(Long redPacketId);

    RedPacketRecordDO findByAppIdAndUserIdAndRedPacketId(Long appId, Long userId, Long redPacketId);

}
