package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 集卡大挑战
 * @date 2023-06-13 16:20
 */
public class CardCollectConstant {

    /** 用户福袋墙 redis-hash（uid） */
    public static final String USER_WALL = "ump:cardCollect:%s";
    /** 礼物墙 */
    public static final List<PrizeItem>     prizeWallTemplate = Lists.newArrayList(
            PrizeItem.builder().prizeName("山河梦世").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-06/1687075316064181.png").prizeKey("SHMS_GIFT").valueGold(888).prizeNum(0).build(),
            PrizeItem.builder().prizeName("球球雪糕").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-05/1683861501402603.png").prizeKey("QQXG_GIFT").valueGold(399).prizeNum(0).build(),
            PrizeItem.builder().prizeName("开心爆了").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-05/1683861034494913.png").prizeKey("KXBL_GIFT").valueGold(166).prizeNum(0).build(),
            PrizeItem.builder().prizeName("财源滚滚").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2021-10/1633948878090690.png").prizeKey("CYGG_GIFT").valueGold(88).prizeNum(0).build(),
            PrizeItem.builder().prizeName("爱的收音机").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-05/1652171625724513.png").prizeKey("ADSYJ_GIFT").valueGold(45).prizeNum(0).build(),
            PrizeItem.builder().prizeName("樱花冰淇淋").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-04/1651218817791744.png").prizeKey("YHBQL_GIFT").valueGold(36).prizeNum(0).build(),
            PrizeItem.builder().prizeName("桃气棒棒糖").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-04/1651215095670485.png").prizeKey("TQBBT_GIFT").valueGold(20).prizeNum(0).build()
    );
    /** 奖品信息 */
    public static final PrizeItem tradePrizeTemplate = PrizeItem.builder().prizeName("余音袅袅").prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-06/1687147453501057.png").prizeKey("YYLL_GIFT").valueGold(1888).prizeNum(0).build();

    /** 兑换锁 redis（uid） */
    public static final String TRADE_LOCK = "ump:cardCollect:tradeLock_%s";

    /** 集卡活动榜单 redis-zSet*/
    public static final String CARD_COLLECT_BOARD = "ump:card_collect_board";
    public static final long MAX_TIMESTAMP = 9999999999999L;
}
