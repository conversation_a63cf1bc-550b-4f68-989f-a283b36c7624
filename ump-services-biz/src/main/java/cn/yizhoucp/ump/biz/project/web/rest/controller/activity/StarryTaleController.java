package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.activity.starryTale.StarryTaleIndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.starryTale.StarryTaleConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.starryTale.StarryTaleDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.starryTale.StarryTaleIndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.starryTale.StarryTaleRankManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class StarryTaleController {
    @Resource
    private StarryTaleIndexManager starryTaleIndexManager;
    @Resource
    private StarryTaleDrawManager starryTaleDrawManager;
    @Resource
    private StarryTaleRankManager starryTaleRankManager;


    @GetMapping("/api/inner/activity/starryTale/claimReward")
    public Result<List<StarryTaleIndexVO.Gift>> claimReward(@RequestParam("row") Integer row, @RequestParam("column") Integer column) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                starryTaleIndexManager.claimReward(row, column)
        );
    }

    @GetMapping("/api/inner/activity/starryTale/get-daily-rank")
    public Result<RankVO> getDailyRank(@RequestParam("date") String date) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                starryTaleIndexManager.getDailyRank(date)
        );
    }

    @GetMapping("/api/inner/starryTale/drawRecord")
    public Result<Object> getDrawLog(@RequestParam("activityCode") String activityCode, @RequestParam("poolCode") String poolCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                starryTaleIndexManager.getDrawRecord(activityCode, poolCode));
    }

    @GetMapping("/api/inner/activity/starryTale/reset")
    public Result<Boolean> reset() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                starryTaleIndexManager.reset()
        );
    }

    @GetMapping("/api/inner/activity/starry_tale/draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starryTaleDrawManager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(StarryTaleConstant.ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/starryTale/sendRankPrize")
    public Result<Boolean> sendRankPrize(@RequestParam(value = "date", required = false) String date) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> starryTaleRankManager.sendRankPrize(date));
    }


}
