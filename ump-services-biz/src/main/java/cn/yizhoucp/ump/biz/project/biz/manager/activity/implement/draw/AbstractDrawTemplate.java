package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.coin.TreasurePoolEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogItemVO;
import cn.yizhoucp.ump.api.vo.activity.eternalLove.DrawLogTimeItemVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogNewVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ActivityReportManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawConfig;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.DrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawLogJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractDrawTemplate implements DrawManager {

    @Resource
    protected SendPrizeManager sendPrizeManager;
    @Resource
    protected ActivityReportManager activityReportManager;
    @Resource
    protected DrawLogJpaDAO drawLogJpaDAO;
    @Resource
    protected DrawPoolItemService drawPoolItemService;
    @Resource
    protected DrawPoolService drawPoolService;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private LogComponent logComponent;


    @Override
    public DrawReturn draw(DrawParam param) {
        DrawContext context = DrawContext.builder().drawParam(param).build();

        //礼盒不下发奖励
        if (ObjectUtil.isNotEmpty(TreasurePoolEnum.fromGiftKey(param.getPoolCode()))) {
            context.getDrawParam().setNoSendPrize(Boolean.TRUE);
        }
        // 资源检查
        resourceCheck(context);
        // 扣除资源
        deductResource(context);
        // 填充奖池、奖品信息
        filledDrawContext(context);
        // 抽奖
        draw(context);
        // 下发奖励
        sendPrize(param, context.getPrizeItemList());
        // 后置处理
        callback(context);
        // 封装出参
        return getResult(context);
    }

    /**
     * 封装出参
     *
     * @param context
     * @return
     */
    public DrawReturn getResult(DrawContext context) {
        List<PrizeItem> prizeItemList = context.getPrizeItemList().stream().map(DrawPoolItemDTO::convert2PrizeItem).collect(Collectors.toList());
        return DrawReturn.builder()
                .hasRealPrize(Boolean.FALSE)
                .prizeItemList(prizeItemList)
                .extData(context.getExtData()).build();
    }

    /**
     * 资源检查
     *
     * @param context
     * @return
     */
    protected abstract void resourceCheck(DrawContext context);

    /**
     * 抽奖实现
     *
     * @param context
     * @return
     */
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    /**
     * 下发奖励
     *
     * @param drawParam
     * @param prizeList
     */
    protected void sendPrize(DrawParam drawParam, List<DrawPoolItemDTO> prizeList) {
        if (Boolean.TRUE.equals(drawParam.getNoSendPrize())) {
            return;
        }
        sendPrizeManager.sendPrize(drawParam.getBaseParam(), prizeList.stream().map(p -> SendPrizeDTO.of(p)).collect(Collectors.toList()));
    }

    /**
     * 资源扣除
     *
     * @param context
     * @return
     */
    protected abstract void deductResource(DrawContext context);

    /**
     * 后置处理
     *
     * @param context
     * @return
     */
    protected abstract void doCallback(DrawContext context);

    /**
     * 抽奖记录包装
     *
     * @param param
     * @param drawLogDOList
     * @return
     */
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }

    /**
     * 记录抽奖记录
     *
     * @param context   抽奖信息上下文
     * @param drawParam 抽奖参数
     */
    protected void recordDrawLog(DrawContext context, DrawParam drawParam){
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());
    }

    /**
     * 后置处理
     *
     * @param context
     * @return
     */
    private void callback(DrawContext context) {
        DrawParam param = context.getDrawParam();
        DrawConfig drawConfig = context.getDrawConfig();

        // 通天塔活动走旧上报流程
        if (ActivityCheckListEnum.CLICK_TO_GOLD.getCode().equals(param.getActivityCode())) {
            clickToGoldDrawTraceHandle(context);
        } else {
            // 验证使用模版上报流程
            if (Objects.nonNull(drawConfig) && Boolean.TRUE.equals(drawConfig.getUseTemplateTrace())) {
                List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
                if (!CollectionUtils.isEmpty(prizeItemList)) {
                    for (DrawPoolItemDTO item : prizeItemList) {
                        for (int i = 0; i < item.getTargetTimes(); i++) {
                            activityReportManager.drawReport(
                                    BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                                    drawConfig.getActivityCode(),
                                    drawConfig.getPoolCode(),
                                    item.getDrawPoolItemDO().getItemKey(),
                                    item.getDrawPoolItemDO().getItemValueGold(),
                                    drawConfig.getActivityAttribute()
                            );
                        }
                    }
                }
            }
        }
        //记录抽奖记录
        recordDrawLog(context, param);

        // 扩展处理
        doCallback(context);
    }

    /**
     * 抽奖记录查询
     * <p>
     * 该方法用于统一索引，谨慎调整查询方式
     *
     * @param param
     * @return
     */
    final public DrawLogVO drawLog(DrawLogParam param) {
        log.debug("param {}", param);
        Example<DrawLogDO> of = Example.of(DrawLogDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .toUid(param.getUid())
                .activityCode(param.getActivityCode())
                .poolCode(param.getPoolCode()).build());
        log.debug("of {}", JSON.toJSONString(of));
        List<DrawLogDO> drawLogDOList = drawLogJpaDAO.findAll(of);
        return DrawLogVO.builder().drawLogItemList(drawLogWrapper(param, drawLogDOList)).build();
    }

    /**
     * 抽奖记录 - 根据活动code + 奖池code
     */
    @Override
    public DrawLogNewVO drawLogLatest(DrawLogParam param) {
        return this.drawLogNew(param, param.getPoolCode());
    }

    public DrawLogNewVO drawLogNew(DrawLogParam param, String poolCode) {
        List<DrawLogDO> drawLogDOList = drawLogJpaDAO.findAll(Example.of(DrawLogDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .toUid(param.getUid())
                .activityCode(param.getActivityCode())
                .poolCode(poolCode)
                .build()));

        List<DrawLogItem> drawLogWrapperList = drawLogWrapper(DrawLogParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(), drawLogDOList);

        DrawLogNewVO drawLogVO = new DrawLogNewVO();
        Map<String, List<DrawLogItemVO>> timeDrawLogItemMap = new HashMap<>();
        for (DrawLogItem drawLogItem : drawLogWrapperList) {
            if (timeDrawLogItemMap.containsKey(drawLogItem.getTime())) {
                timeDrawLogItemMap.get(drawLogItem.getTime()).add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
            } else {
                List<DrawLogItemVO> drawLogItemList = new ArrayList<>();
                drawLogItemList.add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
                timeDrawLogItemMap.put(drawLogItem.getTime(), drawLogItemList);
            }
        }
        List<DrawLogTimeItemVO> drawLogTimeItemList = new ArrayList<>();
        timeDrawLogItemMap.forEach((k, v) -> drawLogTimeItemList.add(DrawLogTimeItemVO.builder().time(k).drawLogItemList(v).build()));
        drawLogTimeItemList.sort((o1, o2) -> o2.getTime().compareTo(o1.getTime()));
        drawLogVO.setDrawLogTimeItemList(drawLogTimeItemList);

        return drawLogVO;
    }

    public DrawLogNewVO drawLogNew(DrawLogParam param) {
        List<DrawLogDO> drawLogDOList = drawLogJpaDAO.findAll(Example.of(DrawLogDO.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .toUid(param.getUid())
                .activityCode(param.getActivityCode())
                .build()));

        List<DrawLogItem> drawLogWrapperList = drawLogWrapper(DrawLogParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(), drawLogDOList);

        DrawLogNewVO drawLogVO = new DrawLogNewVO();
        Map<String, List<DrawLogItemVO>> timeDrawLogItemMap = new HashMap<>();
        for (DrawLogItem drawLogItem : drawLogWrapperList) {
            if (timeDrawLogItemMap.containsKey(drawLogItem.getTime())) {
                timeDrawLogItemMap.get(drawLogItem.getTime()).add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
            } else {
                List<DrawLogItemVO> drawLogItemList = new ArrayList<>();
                drawLogItemList.add(DrawLogItemVO.builder().itemNum(drawLogItem.getItemNum()).text(drawLogItem.getText()).build());
                timeDrawLogItemMap.put(drawLogItem.getTime(), drawLogItemList);
            }
        }
        List<DrawLogTimeItemVO> drawLogTimeItemList = new ArrayList<>();
        timeDrawLogItemMap.forEach((k, v) -> drawLogTimeItemList.add(DrawLogTimeItemVO.builder().time(k).drawLogItemList(v).build()));
        drawLogTimeItemList.sort((o1, o2) -> o2.getTime().compareTo(o1.getTime()));
        drawLogVO.setDrawLogTimeItemList(drawLogTimeItemList);

        return drawLogVO;
    }

    /**
     * 抽奖记录查询（分页）
     * <p>
     * 该方法用于统一索引，谨慎调整查询方式
     *
     * @param param
     * @return
     */
    final public DrawLogVO drawLogByPage(DrawLogParam param) {
        if (Objects.isNull(param.getPageNo()) || Objects.isNull(param.getPageSize())) {
            throw new ServiceException(ErrorCode.MISS_PARAM);
        }
        Page<DrawLogDO> queryResult = drawLogJpaDAO.findAll(Example.of(DrawLogDO.builder()
                        .appId(param.getAppId())
                        .unionId(param.getUnionId())
                        .toUid(param.getUid())
                        .activityCode(param.getActivityCode())
                        .poolCode(param.getPoolCode()).build()),
                PageRequest.of(param.getPageNo(), param.getPageSize()));
        cn.yizhoucp.ms.core.vo.Page<DrawLogItem> page = new cn.yizhoucp.ms.core.vo.Page<>();
        page.setTotalPagesCount(queryResult.getTotalPages());
        page.setHasNext(param.getPageNo() < queryResult.getTotalPages());
        page.setPageIndex(param.getPageNo());
        page.setPageSize(page.getPageSize());
        page.setItems(drawLogWrapper(param, queryResult.getContent()));
        return DrawLogVO.builder().drawLogItemListByPage(page).build();
    }

    protected void filledDrawContext(DrawContext context) {
        // 参数兼容处理
        DrawParam param = context.getDrawParam();
        if (StringUtils.isEmpty(param.getPoolCode())) {
            param.setPoolCode(param.getType());
        }

        // 获取奖池属性
        List<DrawPoolDO> drawPoolList = drawPoolService.getByCode(param.getPoolCode());
        if (CollectionUtils.isEmpty(drawPoolList)) {
            log.error("目标奖池不存在 param:{}", JSON.toJSONString(param));
            throw new ServiceException(ErrorCode.MISS_PARAM, "目标奖池不存在");
        }
        context.setDrawPoolDO(drawPoolList.get(0));

        // 获取奖池奖品列表
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(param.getPoolCode());
        if (CollectionUtils.isEmpty(drawPoolItemDOList)) {
            log.error("目标奖池不存在 param:{}", JSON.toJSONString(param));
            throw new ServiceException(ErrorCode.MISS_PARAM, "奖池列表为空");
        }
        context.setDrawPoolItemDOS(drawPoolItemDOList);
    }

    @Deprecated
    private void clickToGoldDrawTraceHandle(DrawContext context) {
        List<DrawPoolItemDTO> prizeItemList = context.getPrizeItemList();
        DrawParam param = context.getDrawParam();
        if (!CollectionUtils.isEmpty(prizeItemList)) {
            for (DrawPoolItemDTO item : prizeItemList) {
                for (int i = 0; i < item.getTargetTimes(); i++) {
                    // 埋点上报
                    activityReportManager.drawReport(
                            BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                            param.getActivityCode(),
                            param.getPoolCode(),
                            item.getDrawPoolItemDO().getItemKey(),
                            item.getDrawPoolItemDO().getItemValueGold(),
                            null
                    );
                }
            }
        }
    }

}
