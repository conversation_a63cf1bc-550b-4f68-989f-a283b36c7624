package cn.yizhoucp.ump.biz.project.web.rest.controller.activity.common;

import cn.yizhoucp.ump.biz.project.biz.manager.drawPoolPost.DrawPoolPostManager;
import cn.yizhoucp.ump.biz.project.biz.manager.ability.ActivityAbilityManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.umpServices.ability.DrawPostInfoVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.client.BuoyInfoVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活动通用能力
 *
 * @author: lianghu
 */
@RequestMapping("/api/inner/activity-ability")
@RestController
public class ActivityAbilityController {

    @Resource
    private ActivityAbilityManager activityAbilityManager;
    @Resource
    private DrawPoolPostManager drawPoolPostManager;

    @RequestMapping("/get-buoy-info")
    public Result<BuoyInfoVO> getBuoyInfo(Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> activityAbilityManager.getBuoyInfo(appId));
    }

    @RequestMapping("/get-draw-post-info")
    public Result<DrawPostInfoVO> getPostInfo(String activityCode, String type) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> drawPoolPostManager.getDrawPoolPostParam(activityCode, type));
    }

    @RequestMapping("/get-jump-url")
    public Result<JSONObject> getJumpUrl(String jumpCode, String activityCode, String type) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> activityAbilityManager.getJumpUrl(jumpCode, activityCode, type));
    }

}
