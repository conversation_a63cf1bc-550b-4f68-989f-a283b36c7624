package cn.yizhoucp.ump.biz.project.biz.manager.activity.treasureHall;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;


@Service
public class TreasureHallConstant {

    public static final String EXCHANGE_GIFT_SCENE_CODE = "treasureHall_exchange";
    public static final String DRAW_POOL_CODE = "mystery_box_pool";
    public static final Long MIN_RANK_VALUE = 250000L;
    public static final String EXCHANGE_GIFT_CODE = "exchange";
    @Resource
    private RedisManager redisManager;

    public static final String ACTIVITY_CODE = "treasure_hall";

    public static final String DRAW_ITEM_KEY = "ump:activity:treasure_hall_1:drawItem:%s";

    private static final String TASK_IS_REWARDED_KEY = "ump:activity:treasure_hall_1:task_is_rewarded:%s:%s:%s";

    private static final String TASK_PROGRESS_KEY = "ump:activity:treasure_hall_1:task_progress:%s:%s:%s";

    public static final String DAILY_RANK_KEY = "ump:activity:treasure_hall_1:daily:rank:%s";


    public static final String TOTAL_RANK_KEY = "ump:activity:treasure_hall_1:total:rank";

    private static final String GIFT_EXCHANGE_KEY = "ump:activity:treasure_hall_1:exchange:%s:%s";
    private static final String GUARDIAN_ROOM_KEY = "ump:activity:treasure_hall_1:guardian_room:%s";


    public Long getDrawItem(Long uid) {
        if (uid == null) {
            return 0L;
        }
        String drawItemKey = String.format(DRAW_ITEM_KEY, uid);
        return Optional.ofNullable(redisManager.getLong(drawItemKey)).orElse(0L);
    }

    public void incrementDrawItem(Long uid, Long value) {
        String drawItemKey = String.format(DRAW_ITEM_KEY, uid);
        redisManager.incrLong(drawItemKey, value, DateUtil.ONE_MONTH_SECOND);
    }

    public void decrementDrawItem(Long uid, Long num) {
        String drawItemKey = String.format(DRAW_ITEM_KEY, uid);
        redisManager.decrLong(drawItemKey, num, DateUtil.ONE_MONTH_SECOND);
    }

    public Boolean isRewarded(String name, Long uid) {
        String key = String.format(TASK_IS_REWARDED_KEY, uid, name, DateUtil.getNowYyyyMMdd());
        return redisManager.hasKey(key);
    }

    public Long getTaskProgress(String name, Long uid) {
        String key = String.format(TASK_PROGRESS_KEY, uid, name, DateUtil.getNowYyyyMMdd());
        return Optional.ofNullable(redisManager.getLong(key)).orElse(0L);
    }

    public void incrementTaskProgress(String name, Long uid, Long num) {
        String key = String.format(TASK_PROGRESS_KEY, uid, name, DateUtil.getNowYyyyMMdd());
        redisManager.incrLong(key, num, DateUtil.ONE_MONTH_SECOND);
    }

    public Boolean isExchange(Long uid, String giftKey) {
        String key = String.format(GIFT_EXCHANGE_KEY, uid, giftKey);
        return redisManager.hasKey(key);
    }

    public void setExchange(Long uid, String giftKey) {
        String key = String.format(GIFT_EXCHANGE_KEY, uid, giftKey);
        redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public Long getGuardianRoom(Long uid) {
        String key = String.format(GUARDIAN_ROOM_KEY, uid);
        return redisManager.getLong(key);
    }

    public void setGuardianRoom(Long uid, Long roomId) {
        String key = String.format(GUARDIAN_ROOM_KEY, uid);
        redisManager.set(key, roomId, DateUtil.ONE_MONTH_SECOND);
    }

    public void rewarded(Long uid, String name) {
        String key = String.format(TASK_IS_REWARDED_KEY, uid, name, DateUtil.getNowYyyyMMdd());
        redisManager.set(key, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }


    @AllArgsConstructor
    @Getter
    public enum TaskEnum {
        TASK_1(1, 1L, "CHQS_GIFT", 1L),
        TASK_2(2, 1L, "ZSQS_GIFT", 1L),
        TASK_3(3, 2L, "SHSL_GIFT", 1L),
        TASK_4(4, 3L, "ZSTS_GIFT", 1L),
        TASK_5(5, 4L, "SSAL_GIFT", 1L),
        TASK_6(6, 7L, "NSJL_GIFT", 1L),
        TASK_7(7, 9L, "HYXH_GIFT", 1L);

        private final Integer id;
        private final Long rewardNum;
        private final String taskGiftKey;
        private final Long totalProgress;

        public static TaskEnum getByGiftKey(String giftKey) {
            for (TaskEnum taskEnum : values()) {
                if (taskEnum.getTaskGiftKey().equals(giftKey)) {
                    return taskEnum;
                }
            }
            return null;
        }
    }


}
