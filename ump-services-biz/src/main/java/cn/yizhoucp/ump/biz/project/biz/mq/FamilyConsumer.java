package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.clickToGold.TaskCodeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.MissionProcessManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.RushSkyBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_INVITE_JOIN_FAMILY;

/**
 * 家族相关业务事件
 *
 * @author: lianghu
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "family_topic", consumerGroup = "GID_UMP_FAMILY_TOPIC_GROUP")
public class FamilyConsumer extends RocketmqAbstractConsumer {

    @Resource
    private RushSkyBizManager rushSkyBizManager;
    @Resource
    private MissionProcessManager missionProcessManager;

    /** 关注 tag 列表 */
    private static final Set<String> LISTEN_TAG_SET = Sets.newHashSet(TOPIC_INVITE_JOIN_FAMILY.getTagKey());

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String json) {
        if (TOPIC_INVITE_JOIN_FAMILY.getTagKey().equals(tag)) {
            return inviteUserJoinFamily(JSON.parseObject(json));
        }
        return Boolean.FALSE;
    }

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_FAMILY.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return LISTEN_TAG_SET;
    }

    /**
     * 邀请用户加入家族
     *
     * @param jsonObject 请求数据
     * @return boolean
     */
    private boolean inviteUserJoinFamily(JSONObject jsonObject) {
        return rushSkyBizManager.inviteJoinFamily(jsonObject);
    }

    /**
     * 停留家族时间
     *
     * @param jsonObject
     * @return
     */
    private Boolean stayFamily(String unionId, Long appId, JSONObject jsonObject) {
        log.debug("stayFamily msg {}", jsonObject.toJSONString());
        Long uid = jsonObject.getLong("uid");
        missionProcessManager.process(MissionParam.builder().uid(uid).appId(appId).unionId(unionId).taskCode(TaskCodeEnum.stayFamilyOrRoom.name()).bizParam(jsonObject).build());
        return Boolean.TRUE;
    }

}
