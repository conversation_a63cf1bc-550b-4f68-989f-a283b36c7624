package cn.yizhoucp.ump.biz.project.biz.manager.activity.mysteryGuestMG;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 定时任务发放奖励
 */
@Service
@Slf4j
public class MysteryGuestMGJobManager {

    @Resource
    private MysteryGuestMGRankManager rankManager;

    /**
     * 发日榜奖励
     *
     * @return
     */
    public Boolean sendDayLeaderboard(String date) {
        return rankManager.sendDayLeaderboard(date);
    }

    /**
     * 发总榜奖励
     *
     * @return
     */
    public Boolean sendTotalLeaderboard() {
        return rankManager.sendTotalLeaderboard();
    }
}
